# 静态大屏部署方案

## 方案概述

本方案创建了一个独立的静态数据大屏，具有以下特点：

- ✅ 不请求接口，使用静态数据
- ✅ 不被路由拦截，独立访问
- ✅ 不需要登录验证
- ✅ 不需要 layout 布局
- ✅ 通过 nginx 配置独立端口访问
- ✅ 支持全屏显示

## 文件结构

```
├── src/views/static-big-screen/index.vue    # 静态大屏页面
├── src/static-screen-main.ts                # 独立入口文件
├── static-screen.html                       # 独立HTML入口
├── nginx-static-screen.conf                 # Nginx配置
├── Dockerfile.static-screen                 # Docker配置
├── build-static-screen.sh                   # 构建脚本
└── STATIC_SCREEN_DEPLOY.md                  # 部署说明
```

## 本地开发

### 1. 安装依赖

```bash
pnpm install
```

### 2. 开发模式运行

```bash
# 运行主应用（包含静态大屏）
pnpm dev

# 访问静态大屏
http://localhost:8080/static-screen.html
```

### 3. 验证功能

访问静态大屏后，应该能看到：

- ✅ 完整的大屏布局和背景
- ✅ 实时时间显示
- ✅ 公司简介滚动文本
- ✅ ECharts 收益概览图表
- ✅ ECharts 结算管理图表
- ✅ 江西省地图（带数据点）
- ✅ ECharts 柱状图（地区统计）
- ✅ 代理大用户滚动列表
- ✅ ECharts 饼图（用户画像）
- ✅ 销售漏斗图
- ✅ 全屏功能（点击标题）

## 生产部署

### 方式一：传统部署

#### 1. 构建项目

```bash
# 使用构建脚本
chmod +x build-static-screen.sh
./build-static-screen.sh

# 或手动构建
pnpm build:static-screen
```

#### 2. 配置 Nginx

```bash
# 复制nginx配置
sudo cp nginx-static-screen.conf /etc/nginx/sites-available/static-screen
sudo ln -s /etc/nginx/sites-available/static-screen /etc/nginx/sites-enabled/

# 复制构建文件到nginx目录
sudo cp -r dist/* /usr/share/nginx/html/

# 重启nginx
sudo systemctl restart nginx
```

#### 3. 访问地址

- 主应用：http://localhost/
- 静态大屏：http://localhost:8081/

### 方式二：Docker 部署

#### 1. 构建 Docker 镜像

```bash
docker build -f Dockerfile.static-screen -t static-screen-app .
```

#### 2. 运行容器

```bash
docker run -d \
  --name static-screen \
  -p 80:80 \
  -p 8081:8081 \
  static-screen-app
```

#### 3. 访问地址

- 主应用：http://localhost/
- 静态大屏：http://localhost:8081/

### 方式三：Docker Compose 部署

创建 `docker-compose.yml`：

```yaml
version: "3.8"
services:
  static-screen-app:
    build:
      context: .
      dockerfile: Dockerfile.static-screen
    ports:
      - "80:80"
      - "8081:8081"
    restart: unless-stopped
```

运行：

```bash
docker-compose up -d
```

## 自定义配置

### 修改静态数据

编辑 `src/views/static-big-screen/index.vue` 中的 `staticData` 对象：

```typescript
const staticData = ref({
  companyIntro: ["公司简介内容..."],
  contractUser: "1,256",
  targetPower: "85,420"
  // ... 其他数据
});
```

### 修改端口

编辑 `nginx-static-screen.conf` 中的端口配置：

```nginx
server {
    listen 8082;  # 修改为你需要的端口
    # ...
}
```

### 修改样式

静态大屏使用与原大屏相同的样式，如需自定义，可修改 `src/views/static-big-screen/index.vue` 中的样式部分。

## 注意事项

1. **资源路径**：确保所有图片资源路径正确，建议使用绝对路径
2. **浏览器兼容性**：建议使用现代浏览器访问
3. **全屏显示**：点击标题可进入全屏模式
4. **数据更新**：如需更新数据，修改静态数据后重新构建部署
5. **端口冲突**：确保 8081 端口未被占用

## 故障排除

### 1. ECharts 相关错误

**错误信息**: `useECharts: echarts未绑定到globalProperties`
**解决方案**:

- 确保在 `src/static-screen-main.ts` 中正确导入和使用了 `useEcharts`
- 检查 `src/plugins/echarts/index.ts` 文件是否存在

**错误信息**: `Renderer 'undefined' is not imported`
**解决方案**:

- 当前版本已使用简化的图表展示，不依赖 ECharts 渲染器
- 如需使用真实图表，请确保正确导入 ECharts 渲染器

### 2. 组件导入错误

**错误信息**: `Module has no default export`
**解决方案**:

- 当前版本已移除对复杂图表组件的依赖
- 使用 CSS 和 HTML 实现的简化图表展示

### 3. 滚动组件错误

**错误信息**: `Cannot find module 'vue3-scroll-seamless'`
**解决方案**:

- 已在代码中添加 `@ts-ignore` 注释
- 如果仍有问题，可以安装类型定义: `pnpm add -D @types/vue3-scroll-seamless`

### 4. 页面无法访问

- 检查 nginx 配置是否正确
- 检查端口是否被占用: `netstat -tulpn | grep :8081`
- 检查防火墙设置: `sudo ufw status`

### 5. 样式显示异常

- 检查静态资源路径
- 确认 CSS 文件是否正确加载
- 检查浏览器控制台是否有 404 错误

### 6. 开发环境测试

使用提供的测试脚本:

```bash
chmod +x test-static-screen.sh
./test-static-screen.sh
```

## 技术栈

- Vue 3 + TypeScript
- Element Plus
- ECharts
- Vite
- Nginx
- Docker
