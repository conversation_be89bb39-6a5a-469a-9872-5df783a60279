# 静态大屏部署方案

## 方案概述

本方案创建了一个独立的静态数据大屏，具有以下特点：
- ✅ 不请求接口，使用静态数据
- ✅ 不被路由拦截，独立访问
- ✅ 不需要登录验证
- ✅ 不需要layout布局
- ✅ 通过nginx配置独立端口访问
- ✅ 支持全屏显示

## 文件结构

```
├── src/views/static-big-screen/index.vue    # 静态大屏页面
├── src/static-screen-main.ts                # 独立入口文件
├── static-screen.html                       # 独立HTML入口
├── nginx-static-screen.conf                 # Nginx配置
├── Dockerfile.static-screen                 # Docker配置
├── build-static-screen.sh                   # 构建脚本
└── STATIC_SCREEN_DEPLOY.md                  # 部署说明
```

## 本地开发

### 1. 安装依赖
```bash
pnpm install
```

### 2. 开发模式运行
```bash
# 运行主应用（包含静态大屏）
pnpm dev

# 访问静态大屏
http://localhost:8080/static-screen.html
```

## 生产部署

### 方式一：传统部署

#### 1. 构建项目
```bash
# 使用构建脚本
chmod +x build-static-screen.sh
./build-static-screen.sh

# 或手动构建
pnpm build:static-screen
```

#### 2. 配置Nginx
```bash
# 复制nginx配置
sudo cp nginx-static-screen.conf /etc/nginx/sites-available/static-screen
sudo ln -s /etc/nginx/sites-available/static-screen /etc/nginx/sites-enabled/

# 复制构建文件到nginx目录
sudo cp -r dist/* /usr/share/nginx/html/

# 重启nginx
sudo systemctl restart nginx
```

#### 3. 访问地址
- 主应用：http://localhost/
- 静态大屏：http://localhost:8081/

### 方式二：Docker部署

#### 1. 构建Docker镜像
```bash
docker build -f Dockerfile.static-screen -t static-screen-app .
```

#### 2. 运行容器
```bash
docker run -d \
  --name static-screen \
  -p 80:80 \
  -p 8081:8081 \
  static-screen-app
```

#### 3. 访问地址
- 主应用：http://localhost/
- 静态大屏：http://localhost:8081/

### 方式三：Docker Compose部署

创建 `docker-compose.yml`：
```yaml
version: '3.8'
services:
  static-screen-app:
    build:
      context: .
      dockerfile: Dockerfile.static-screen
    ports:
      - "80:80"
      - "8081:8081"
    restart: unless-stopped
```

运行：
```bash
docker-compose up -d
```

## 自定义配置

### 修改静态数据
编辑 `src/views/static-big-screen/index.vue` 中的 `staticData` 对象：

```typescript
const staticData = ref({
  companyIntro: ['公司简介内容...'],
  contractUser: '1,256',
  targetPower: '85,420',
  // ... 其他数据
});
```

### 修改端口
编辑 `nginx-static-screen.conf` 中的端口配置：

```nginx
server {
    listen 8082;  # 修改为你需要的端口
    # ...
}
```

### 修改样式
静态大屏使用与原大屏相同的样式，如需自定义，可修改 `src/views/static-big-screen/index.vue` 中的样式部分。

## 注意事项

1. **资源路径**：确保所有图片资源路径正确，建议使用绝对路径
2. **浏览器兼容性**：建议使用现代浏览器访问
3. **全屏显示**：点击标题可进入全屏模式
4. **数据更新**：如需更新数据，修改静态数据后重新构建部署
5. **端口冲突**：确保8081端口未被占用

## 故障排除

### 1. 页面无法访问
- 检查nginx配置是否正确
- 检查端口是否被占用
- 检查防火墙设置

### 2. 样式显示异常
- 检查静态资源路径
- 确认CSS文件是否正确加载

### 3. 图表不显示
- 检查ECharts依赖是否正确加载
- 查看浏览器控制台错误信息

## 技术栈

- Vue 3 + TypeScript
- Element Plus
- ECharts
- Vite
- Nginx
- Docker
