<template>
  <div ref="chartRef" :style="{ height, width }" />
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  Ref,
  onMounted,
  watch,
  onActivated
} from "vue";
import type { EChartsOption } from "echarts";
import * as echarts from "echarts/core";
import { useAppStoreHook } from "@/store/modules/app";
import { useECharts, delay } from "@pureadmin/utils";
import { triggerWindowResize } from "@/utils/event";

export default defineComponent({
  props: {
    width: {
      type: String as PropType<string>,
      default: "100%"
    },
    height: {
      type: String as PropType<string>,
      default: "calc(100vh - 78px)"
    },
    series: {
      type: Array as PropType<Array<object>>,
      default: () => []
    }
  },
  emits: ["update"],
  setup(props, { emit }) {
    const chartRef = ref<HTMLDivElement | null>(null);
    
    // 静态数据，不请求API
    const seriesData = ref([
      { name: '南昌市', value: 1250 },
      { name: '九江市', value: 980 },
      { name: '景德镇市', value: 750 },
      { name: '萍乡市', value: 650 },
      { name: '新余市', value: 580 },
      { name: '鹰潭市', value: 520 },
      { name: '赣州市', value: 1100 },
      { name: '宜春市', value: 890 },
      { name: '上饶市', value: 720 },
      { name: '吉安市', value: 680 },
      { name: '抚州市', value: 590 }
    ]);
    
    const geoCoordMap = {
      赣州市: [115.30001, 25.82097],
      吉安市: [114.586373, 27.111699],
      上饶市: [117.571185, 28.44442],
      九江市: [115.992811, 29.512034],
      抚州市: [116.358351, 27.68385],
      宜春市: [114.391136, 28.2043],
      南昌市: [115.892151, 28.476493],
      景德镇市: [117.214664, 28.99256],
      萍乡市: [113.852186, 27.322946],
      鹰潭市: [117.193838, 28.038638],
      新余市: [114.660835, 27.710834]
    };
    
    function convertData(data) {
      const res = [];
      for (var i = 0; i < data.length; i++) {
        const geoCoord = geoCoordMap[data[i].name];
        if (geoCoord) {
          res.push({
            name: data[i].name,
            value: geoCoord.concat(data[i].value)
          });
        }
      }
      return res;
    }
    
    const tooltipValue = ref();
    const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>);
    
    // 注册地图，检查mapJSON是否存在
    if (window.mapJSON) {
      echarts.registerMap("jiangxi", window.mapJSON);
    }
    
    const getOption = (): EChartsOption => {
      return {
        tooltip: {
          renderMode: "html",
          triggerOn: "click",
          enterable: true,
          backgroundColor: "transparent",
          borderColor: "transparent",
          borderWidth: 0,
          padding: 0,
          extraCssText: "box-shadow: none;",
          formatter: function (params) {
            tooltipValue.value = params;
            let res =
              '<div style="box-shadow: none;" class="custom-tooltip"><h2 style="color:#fff;font-size: 16px;padding-top:38px;text-align:center;">' +
              params.name +
              '</h2><div style="color:#fff;font-size: 14px;text-align:center;padding-bottom:38px;">用户数量：' +
              (params.value || 0) +
              '</div></div>';
            return res;
          }
        },
        geo: {
          map: "jiangxi",
          aspectScale: 0.85,
          zoom: 1.05,
          roam: false,
          top: "8%",
          left: "5%",
          right: "5%",
          bottom: "8%",
          itemStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "#4aa2eb"
                },
                {
                  offset: 1,
                  color: "#4799dc"
                }
              ],
              global: false
            },
            borderWidth: 0,
            shadowColor: "#065fcf",
            shadowOffsetX: 5,
            shadowOffsetY: 30
          }
        },
        series: [
          {
            type: "map",
            roam: false,
            label: {
              show: true,
              color: "rgba(255,255,255,.65)"
            },
            itemStyle: {
              areaColor: "#4aa2eb",
              borderWidth: 2,
              borderColor: "#64e1ff"
            },
            emphasis: {
              label: {
                color: "rgba(255,255,255,.65)"
              },
              itemStyle: {
                areaColor: "rgba(20,30,64,0)",
                borderWidth: 1,
                borderColor: "rgba(34,120,255)"
              }
            },
            top: "8%",
            left: "5%",
            right: "5%",
            bottom: "8%",
            zoom: 1.05,
            map: "jiangxi"
          },
          {
            name: "点",
            type: "scatter",
            coordinateSystem: "geo",
            symbol: "circle",
            symbolSize: function (val) {
              return Math.max(val[2] / 100, 8);
            },
            label: {
              formatter: "{b}",
              position: "right",
              show: false
            },
            itemStyle: {
              color: {
                type: "radial",
                x: 0.5,
                y: 0.5,
                r: 0.5,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(255, 255, 255, 0)"
                  },
                  {
                    offset: 1,
                    color: "rgba(255, 255, 255, 1)"
                  }
                ],
                global: false
              }
            },
            emphasis: {
              scale: true
            },
            data: convertData(seriesData.value)
          }
        ]
      };
    };

    function handleDetail() {
      console.log('Static map detail clicked');
      // 静态版本不需要处理详情
    }

    // 设置全局处理函数
    window.handleDetail = handleDetail;

    watch(
      () => useAppStoreHook().getSidebarStatus,
      () => {
        delay(600).then(() => resize());
      }
    );
    
    watch(
      () => props,
      () => setOptions(getOption() as EChartsOption),
      {
        immediate: true,
        deep: true
      }
    );

    onMounted(() => {
      delay(300).then(() => resize());
    });
    
    onActivated(() => triggerWindowResize());
    
    return { chartRef };
  }
});
</script>

<style scoped>
.custom-tooltip {
  background: url("/src/assets/images/BigScreen/tooltip-bg.png") no-repeat;
  background-size: 100% 100%;
  width: 200px;
  height: 120px;
}
</style>
