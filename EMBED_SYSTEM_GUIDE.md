# 智慧售电平台 - 嵌入系统使用指南

## 概述

本指南详细说明如何将智慧售电平台的页面嵌入到其他系统中，包括配置方法、登录状态管理和最佳实践。

## 功能特性

### ✅ 已实现功能

1. **菜单路由提取** - 完整的系统路由文档
2. **布局配置控制** - 灵活的头部和菜单栏显示控制
3. **嵌入模式支持** - 多种预设的嵌入模式
4. **登录状态管理** - iframe嵌套的登录状态保持方案
5. **演示页面** - 可视化的配置和测试界面

### 📋 相关文档

- [MENU_ROUTES.md](./MENU_ROUTES.md) - 完整的菜单路由文档
- [IFRAME_LOGIN_STATE.md](./IFRAME_LOGIN_STATE.md) - iframe登录状态管理说明

## 快速开始

### 1. 访问嵌入演示页面

```
http://your-domain.com/embed/demo
```

在演示页面中，您可以：
- 实时查看当前布局配置
- 切换不同的显示模式
- 生成嵌入代码
- 复制嵌入URL

### 2. 基本嵌入方式

#### iframe嵌入（推荐）
```html
<!-- 基本嵌入 -->
<iframe 
  src="http://your-domain.com/customer-record/index?embed=iframe" 
  width="100%" 
  height="600px" 
  frameborder="0">
</iframe>

<!-- 最小化模式 -->
<iframe 
  src="http://your-domain.com/customer-record/index?embed=minimal" 
  width="100%" 
  height="600px" 
  frameborder="0">
</iframe>
```

#### 直接链接
```html
<!-- 隐藏头部和侧边栏 -->
<a href="http://your-domain.com/customer-record/index?hideHeader=true&hideSidebar=true" 
   target="_blank">
  打开客户档案
</a>
```

## 配置选项

### 1. 预设模式

| 模式 | 说明 | URL参数 | 适用场景 |
|------|------|---------|----------|
| normal | 正常模式 | 无 | 完整功能访问 |
| iframe | iframe模式 | `?embed=iframe` | 嵌入到其他系统 |
| minimal | 最小化模式 | `?embed=minimal` | 只显示页面内容 |
| simple | 简化模式 | `?embed=simple` | 保留基本导航 |

### 2. 自定义配置

| 参数 | 说明 | 示例 |
|------|------|------|
| hideHeader | 隐藏头部 | `?hideHeader=true` |
| hideSidebar | 隐藏侧边栏 | `?hideSidebar=true` |
| hideTabs | 隐藏标签页 | `?hideTabs=true` |
| hideFooter | 隐藏页脚 | `?hideFooter=true` |
| hideAll | 隐藏所有UI | `?hideAll=true` |

### 3. 组合使用
```
http://your-domain.com/load-forecasting/analysis?hideHeader=true&hideSidebar=true&hideTabs=true
```

## 登录状态管理

### 问题说明

**Q: iframe嵌套的框架在一个页面登录后再进入其他菜单页面时登录状态会丢失吗？**

**A: 通常不会丢失，但需要正确的实现方式。**

### 解决方案

#### 1. Token传递方式（推荐）

```javascript
// 方式1: URL参数传递
const token = localStorage.getItem('access_token');
const iframeUrl = `http://your-domain.com/customer-record/index?token=${token}&embed=iframe`;

// 方式2: postMessage通信
window.addEventListener('load', () => {
  const iframe = document.getElementById('embedded-iframe');
  iframe.contentWindow.postMessage({
    type: 'AUTH_TOKEN',
    token: localStorage.getItem('access_token')
  }, 'http://your-domain.com');
});
```

#### 2. 同域名Cookie共享

```javascript
// 设置Cookie为顶级域名
document.cookie = `session_id=${sessionId}; domain=.your-domain.com; path=/`;
```

#### 3. 自动token刷新

系统已内置自动token刷新机制，确保长时间使用不会掉线。

## 开发集成

### 1. 在Vue项目中使用布局配置

```typescript
// 导入布局配置hooks
import { useLayoutConfig, useLayoutDisplay } from '@/hooks/useLayoutConfig';

export default {
  setup() {
    const { applyPreset, toggleHeader, toggleSidebar } = useLayoutConfig();
    const layoutDisplay = useLayoutDisplay();

    // 应用iframe模式
    const enterIframeMode = () => {
      applyPreset('iframe');
    };

    // 检查当前状态
    const isHeaderVisible = layoutDisplay.showHeader();
    const isSidebarVisible = layoutDisplay.showSidebar();

    return {
      enterIframeMode,
      isHeaderVisible,
      isSidebarVisible,
      toggleHeader,
      toggleSidebar
    };
  }
};
```

### 2. 动态生成嵌入URL

```typescript
import { layoutConfigManager } from '@/config/layout';

// 生成不同模式的嵌入URL
const generateEmbedUrls = (basePath: string) => {
  return {
    iframe: layoutConfigManager.generateEmbedUrl(basePath, { preset: 'iframe' }),
    minimal: layoutConfigManager.generateEmbedUrl(basePath, { preset: 'minimal' }),
    custom: layoutConfigManager.generateEmbedUrl(basePath, {
      hideHeader: true,
      hideSidebar: true
    })
  };
};
```

### 3. 认证状态管理

```typescript
// 使用内置的iframe认证管理器
import { iframeAuth } from '@/utils/iframeAuth';

// 设置token
iframeAuth.setToken('your-access-token');

// 获取token
const token = iframeAuth.getToken();

// 监听登出事件
window.addEventListener('message', (event) => {
  if (event.data.type === 'LOGOUT') {
    iframeAuth.logout();
  }
});
```

## 最佳实践

### 1. 安全性

- ✅ 使用HTTPS协议
- ✅ 验证iframe来源域名
- ✅ 设置合理的CSP策略
- ✅ 定期刷新认证token

### 2. 性能优化

- ✅ 按需加载页面资源
- ✅ 缓存静态资源
- ✅ 优化iframe尺寸
- ✅ 避免不必要的重复请求

### 3. 用户体验

- ✅ 提供加载状态指示
- ✅ 优雅处理错误情况
- ✅ 保持一致的视觉风格
- ✅ 支持响应式布局

### 4. 开发调试

- ✅ 使用浏览器开发者工具
- ✅ 检查控制台错误信息
- ✅ 验证网络请求状态
- ✅ 测试不同屏幕尺寸

## 常见问题

### Q1: 嵌入页面显示不完整？
**A**: 检查iframe的宽高设置，确保尺寸足够显示内容。

### Q2: 登录状态经常丢失？
**A**: 确认token传递机制是否正确，检查token有效期设置。

### Q3: 跨域问题如何解决？
**A**: 配置正确的CORS策略，或使用同域名部署。

### Q4: 如何自定义样式？
**A**: 通过CSS变量或主题配置来自定义外观。

## 技术支持

如果在使用过程中遇到问题，请：

1. 查看浏览器控制台错误信息
2. 检查网络请求状态
3. 参考相关文档说明
4. 联系技术支持团队

---

## 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 完成菜单路由提取
- ✅ 实现布局配置控制
- ✅ 添加嵌入模式支持
- ✅ 完成登录状态管理方案
- ✅ 创建演示和文档

---

*最后更新时间: ${new Date().toLocaleString()}*
