@echo off
chcp 65001 >nul

echo 🚀 启动静态大屏开发服务器...

REM 检查pnpm是否安装
where pnpm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ pnpm 未安装，请先安装 pnpm
    echo    npm install -g pnpm
    pause
    exit /b 1
)

REM 检查node_modules
if not exist "node_modules" (
    echo 📦 安装依赖...
    pnpm install
)

echo 🔧 启动开发服务器...
echo.
echo 📺 静态大屏访问地址: http://localhost:8080/static-screen.html
echo 🌐 主应用访问地址: http://localhost:8080/
echo.
echo 按 Ctrl+C 停止服务器
echo.

REM 启动开发服务器
pnpm dev
