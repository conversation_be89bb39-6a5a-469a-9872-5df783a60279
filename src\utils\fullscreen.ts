// 全屏状态管理工具
export class FullscreenManager {
  private element: HTMLElement | null = null;
  private callbacks: {
    onEnter?: () => void;
    onExit?: () => void;
    onChange?: (isFullscreen: boolean) => void;
  } = {};

  constructor(elementId?: string) {
    if (elementId) {
      this.element = document.getElementById(elementId);
    }
    this.bindEvents();
  }

  private bindEvents() {
    // 监听全屏状态变化
    document.addEventListener('fullscreenchange', this.handleFullscreenChange.bind(this));
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange.bind(this));
    document.addEventListener('mozfullscreenchange', this.handleFullscreenChange.bind(this));
    document.addEventListener('MSFullscreenChange', this.handleFullscreenChange.bind(this));
  }

  private handleFullscreenChange() {
    const isFullscreen = this.isFullscreen();
    
    if (isFullscreen) {
      this.callbacks.onEnter?.();
      this.optimizeForFullscreen();
    } else {
      this.callbacks.onExit?.();
      this.optimizeForWindow();
    }
    
    this.callbacks.onChange?.(isFullscreen);
  }

  private optimizeForFullscreen() {
    // 全屏状态下的优化
    if (this.element) {
      this.element.style.height = '100vh';
      this.element.style.overflow = 'hidden';
      
      // 调整内容区域
      const mainContent = this.element.querySelector('.main-content') as HTMLElement;
      if (mainContent) {
        mainContent.style.height = 'calc(100vh - 100px)';
        mainContent.style.overflow = 'hidden';
      }
    }
  }

  private optimizeForWindow() {
    // 窗口状态下的优化
    if (this.element) {
      this.element.style.height = 'auto';
      this.element.style.minHeight = '1080px';
      this.element.style.overflow = 'auto';
      
      // 调整内容区域
      const mainContent = this.element.querySelector('.main-content') as HTMLElement;
      if (mainContent) {
        mainContent.style.height = 'auto';
        mainContent.style.overflow = 'visible';
      }
    }
  }

  public isFullscreen(): boolean {
    return !!(
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).mozFullScreenElement ||
      (document as any).msFullscreenElement
    );
  }

  public async enterFullscreen(element?: HTMLElement): Promise<void> {
    const targetElement = element || this.element || document.documentElement;
    
    try {
      if (targetElement.requestFullscreen) {
        await targetElement.requestFullscreen();
      } else if ((targetElement as any).webkitRequestFullscreen) {
        await (targetElement as any).webkitRequestFullscreen();
      } else if ((targetElement as any).mozRequestFullScreen) {
        await (targetElement as any).mozRequestFullScreen();
      } else if ((targetElement as any).msRequestFullscreen) {
        await (targetElement as any).msRequestFullscreen();
      }
    } catch (error) {
      console.error('Failed to enter fullscreen:', error);
    }
  }

  public async exitFullscreen(): Promise<void> {
    try {
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      } else if ((document as any).webkitExitFullscreen) {
        await (document as any).webkitExitFullscreen();
      } else if ((document as any).mozCancelFullScreen) {
        await (document as any).mozCancelFullScreen();
      } else if ((document as any).msExitFullscreen) {
        await (document as any).msExitFullscreen();
      }
    } catch (error) {
      console.error('Failed to exit fullscreen:', error);
    }
  }

  public async toggleFullscreen(element?: HTMLElement): Promise<void> {
    if (this.isFullscreen()) {
      await this.exitFullscreen();
    } else {
      await this.enterFullscreen(element);
    }
  }

  public onFullscreenChange(callbacks: {
    onEnter?: () => void;
    onExit?: () => void;
    onChange?: (isFullscreen: boolean) => void;
  }) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  public destroy() {
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange.bind(this));
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange.bind(this));
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange.bind(this));
    document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange.bind(this));
  }
}
