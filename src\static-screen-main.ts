import { createApp } from "vue";
import EChartsStaticBigScreen from "@/views/static-big-screen/echarts.vue";
import ElementPlus from "element-plus";
import { useEcharts } from "@/plugins/echarts";
import { MotionPlugin } from "@vueuse/motion";

// 引入样式
import "element-plus/dist/index.css";
import "@/style/reset.scss";
import "@/style/index.scss";
import "@/style/tailwind.css";
import "@/assets/iconfont/iconfont.js";
import "@/assets/iconfont/iconfont.css";

// 创建独立的Vue应用实例
const app = createApp(EChartsStaticBigScreen);

// 配置全局属性（模拟主应用的配置）
const mockConfig = {
  Version: "4.5.0",
  Title: "智慧售电平台-静态大屏",
  BaseUrl: ""
};

app.config.globalProperties.$config = mockConfig;

// 使用插件
app.use(ElementPlus)
   .use(useEcharts)  // 重要：配置ECharts到全局属性
   .use(MotionPlugin);

// 挂载到DOM
app.mount("#static-screen-app");
