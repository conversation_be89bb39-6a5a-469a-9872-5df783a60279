<template>
  <section id="staticFullDiv" class="big-screen-box" style="margin: 0">
    <div class="top-title">
      <img
        class="title-bg"
        src="/src/assets/images/BigScreen/title-bg.png"
        alt=""
      />
      <div class="title-box" @click="toggle">
        <img
          class="logo"
          src="/src/assets/images/BigScreen/stateGrid.png"
          alt=""
        />
        <div class="title">国网江西综合能源服务有限公司-智慧售电平台</div>
      </div>
    </div>
    <!-- 右侧时间 -->
    <div class="time">{{ dayjs(time).format("YYYY-MM-DD HH:mm:ss") }}</div>
    <div class="left">
      <img src="/src/assets/images/BigScreen/left-bg.png" alt="" />
    </div>
    <div class="bottom">
      <img src="/src/assets/images/BigScreen/bottom-bg.png" alt="" />
    </div>
    <div class="right">
      <img src="/src/assets/images/BigScreen/right-bg.png" alt="" />
    </div>
    <!-- 主题内容部分 -->
    <div class="main-content">
      <div class="edge-module">
        <div class="left-module1">
          <div class="title-box">
            <div class="title">公司简介</div>
          </div>
          <div class="module-content">
            <vue3ScrollSeamless
              class="box"
              :dataList="staticData.companyIntro"
              :classOptions="classOptions"
            >
              <ul class="ui-wrap">
                <li
                  class="li-item"
                  v-for="(item, i) of staticData.companyIntro"
                  :key="i"
                >
                  <p style="text-indent: 2em">{{ item }}</p>
                </li>
              </ul>
            </vue3ScrollSeamless>
          </div>
        </div>
        <div class="left-module2">
          <div class="title-box">
            <div class="title">收益概览</div>
            <div class="value pb-[6px]">
              年累计：{{ staticData.totalRevenue }}万元
            </div>
          </div>
          <div class="mt-[30px]">
            <ChartsPictorialBar
              :legend-data="monenyLegend"
              :series="monenySeries"
              y-axis-name1="万元"
              height="250px"
            />
          </div>
        </div>
        <div class="left-module3">
          <div class="title-box">
            <div class="title">结算管理</div>
            <div class="value pb-[6px]">
              年累计：{{ staticData.totalSettlement }}MWh
            </div>
          </div>
          <div class="mt-[30px]">
            <ChartsPictorialBar
              :legend-data="settleLegend"
              :series="settleSeries"
              y-axis-name1="MWh"
              y-axis-name2="元/MWh"
              height="250px"
            />
          </div>
        </div>
      </div>
      <div class="center-module">
        <div class="center-card">
          <div
            class="card-wrapper"
            style="display: flex; justify-content: center"
          >
            <div class="card-box">
              <div class="card-title">签约用户</div>
              <div class="card-value">
                <div class="value">{{ staticData.contractUser }}</div>
                <div class="unit">家</div>
              </div>
            </div>
            <div class="card-box">
              <div class="card-title">目标电量</div>
              <div class="card-value">
                <div class="value">{{ staticData.targetPower }}</div>
                <div class="unit">MWh</div>
              </div>
            </div>
          </div>
          <div class="card-wrapper mt-[15px]">
            <div class="card-box">
              <div class="card-title">合同电量</div>
              <div class="card-value">
                <div class="value">{{ staticData.contractElectricity }}</div>
                <div class="unit">MWh</div>
              </div>
            </div>
            <div class="card-box">
              <div class="card-title">结算电量</div>
              <div class="card-value">
                <div class="value">{{ staticData.settleElectricity }}</div>
                <div class="unit">MWh</div>
              </div>
            </div>
            <div class="card-box">
              <div class="card-title">完成率</div>
              <div class="card-value">
                <div class="value">{{ staticData.completionRate }}</div>
                <div class="unit">%</div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <ChartsMap :series="mapSeries" height="700px" />
        </div>
        <div class="center-content">
          <!-- 柱状图 -->
          <div class="chart-box">
            <ChartsBar :y-data="areayAxis" :series="areaSeries" />
          </div>
        </div>
      </div>
      <div class="edge-module">
        <div class="right-module1">
          <div class="title-box">
            <div class="title">代理大用户合同电量(MWh)</div>
          </div>
          <vue3ScrollSeamless
            class="scroll-warp content"
            :dataList="userList"
            :classOptions="classOptions"
          >
            <div v-for="(item, index) in userList" :key="index">
              <div
                class="li"
                v-for="(subItem, subIndex) in item"
                :key="subIndex"
              >
                <div class="name" :title="subItem.name">{{ subItem.name }}</div>
                <div class="value">{{ subItem.value }}</div>
              </div>
            </div>
          </vue3ScrollSeamless>
        </div>
        <div class="right-module2">
          <div class="title-box">
            <div class="title">用户画像分析</div>
          </div>
          <div class="mt-[30px]">
            <ChartsPie
              class="chartsPie-box"
              height="220px"
              :series="pieSeries"
            />
          </div>
        </div>
        <div class="right-module3">
          <div class="title-box">
            <div class="title">销售漏斗</div>
          </div>
          <div class="funnel-box">
            <img
              src="/src/assets/images/BigScreen/funel.png"
              class="h-[170px] w-[320px]"
              alt=""
            />
            <div class="legend-box">
              <div
                class="content"
                v-for="(item, index) in funnelSeries"
                :key="index"
              >
                <div class="label">{{ item.name }}</div>
                <div class="value">({{ item.value }})</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import dayjs, { Dayjs } from "dayjs";
import ChartsPie from "../big-screen/components/charts-pie.vue";
import ChartsBar from "../big-screen/components/charts-bar.vue";
import ChartsMap from "../big-screen/components/charts-map.vue";
import ChartsPictorialBar from "../big-screen/components/charts-pictorialBar.vue";
import screenFull from "screenfull";
import { useIntervalFn } from "@vueuse/core";
import { vue3ScrollSeamless } from "vue3-scroll-seamless";
import type { EChartsOption } from "echarts";

defineOptions({
  name: "StaticBigScreen"
});

const time = ref<Dayjs>(dayjs());

const classOptions = {
  step: 0.1,
  limitMoveNum: 1
};

// 静态数据配置
const staticData = ref({
  companyIntro: [
    '江西赣能能源服务有限公司（以下简称"赣能能源"）于2019年5月23日，由江西赣能股份有限公司、江西省天然气集团有限公司和中国电建江西省电力设计院有限公司共同出资设立。',
    '公司致力于打造成为集发、售、配电及油、气、节能服务于一体的综合能源服务商。从售电业务起步，逐步涉入配电，增值服务及综合能源利用领域。秉承"赣能能源，您的能源贴心管家"的服务理念，为用户提供一站式的全方位、多元化综合能源及个性化增值服务。',
    "江西赣能能源还承接了赣能股份营销中心的发电侧电力营销职能，负责保障和落实赣能股份所属电厂市场交易电量合同。",
    "通过对各项交易规则和政策文件的研究，加强与其他发电集团的协同与交流合作，在省内电力市场化交易及相关规则制定过程中努力维护发电企业及公司的合理利益和市场地位。"
  ],
  contractUser: "1,256",
  targetPower: "85,420",
  contractElectricity: "78,650",
  settleElectricity: "76,890",
  completionRate: "92.5",
  totalRevenue: "12,580",
  totalSettlement: "76,890"
});

// 地区统计用户y轴
const areayAxis = ref<string[]>([
  "南昌市",
  "九江市",
  "景德镇市",
  "萍乡市",
  "新余市",
  "鹰潭市",
  "赣州市",
  "宜春市",
  "上饶市",
  "吉安市",
  "抚州市"
]);
const areaSeries = ref<number[]>([
  1250, 980, 750, 650, 580, 520, 1100, 890, 720, 680, 590
]);

// 地图数据
const mapSeries = ref([
  { name: "南昌市", value: 1 },
  { name: "九江市", value: 2 },
  { name: "景德镇市", value: 3 },
  { name: "萍乡市", value: 4 },
  { name: "新余市", value: 5 },
  { name: "鹰潭市", value: 6 },
  { name: "赣州市", value: 7 },
  { name: "宜春市", value: 8 },
  { name: "上饶市", value: 9 },
  { name: "吉安市", value: 10 },
  { name: "抚州市", value: 11 }
]);

// 代理大用户列表
const userList = ref([
  [
    { name: "江西铜业集团有限公司", value: "15,680" },
    { name: "新钢集团有限公司", value: "12,450" },
    { name: "江西方大钢铁集团", value: "11,230" },
    { name: "江西晨鸣纸业有限公司", value: "9,870" }
  ],
  [
    { name: "江西理文化工有限公司", value: "8,650" },
    { name: "江西赛维LDK太阳能", value: "7,890" },
    { name: "江西洪都航空工业集团", value: "6,780" },
    { name: "江西昌河汽车有限责任公司", value: "5,920" }
  ]
]);

function setTime() {
  time.value = dayjs();
}

// 收益概览数据
const monenySeries = ref([
  {
    data: [850, 920, 780, 1100, 1250, 1380, 1520, 1680, 1450, 1320, 1180, 1050],
    type: "bar",
    barWidth: 15,
    itemStyle: {
      color: {
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        type: "linear",
        global: false,
        colorStops: [
          { offset: 0, color: "#0b9eff" },
          { offset: 1, color: "#63caff" }
        ]
      }
    },
    label: { show: false }
  },
  {
    data: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
    type: "pictorialBar",
    symbol: "diamond",
    symbolOffset: [0, "50%"],
    tooltip: { show: false },
    symbolSize: [15, 15]
  },
  {
    name: "收益",
    data: [850, 920, 780, 1100, 1250, 1380, 1520, 1680, 1450, 1320, 1180, 1050],
    type: "pictorialBar",
    symbolPosition: "end",
    symbol: "diamond",
    symbolOffset: [0, "-50%"],
    symbolSize: [15, 12],
    zlevel: 2
  },
  {
    data: [
      1680, 1680, 1680, 1680, 1680, 1680, 1680, 1680, 1680, 1680, 1680, 1680
    ],
    type: "bar",
    barWidth: 15,
    barGap: "-100%",
    itemStyle: { color: "#1462BD", opacity: 0.2 },
    tooltip: { show: false },
    zlevel: -1
  },
  {
    data: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
    type: "pictorialBar",
    symbol: "diamond",
    symbolOffset: [0, "50%"],
    tooltip: { show: false },
    symbolSize: [15, 15],
    zlevel: -2
  },
  {
    data: [
      1680, 1680, 1680, 1680, 1680, 1680, 1680, 1680, 1680, 1680, 1680, 1680
    ],
    type: "pictorialBar",
    symbolPosition: "end",
    symbol: "diamond",
    itemStyle: { color: "#1462BD", opacity: 0.2 },
    tooltip: { show: false },
    symbolOffset: [0, "-50%"],
    symbolSize: [15, 12],
    zlevel: -1
  }
]);

const monenyLegend: EChartsOption["legend"] = [
  {
    name: "收益",
    textStyle: { color: "#EAF1FF" },
    itemStyle: { color: "#2762E3" },
    icon: "roundRect"
  }
];

// 结算管理数据
const settleSeries = ref([
  {
    yAxisIndex: 0,
    name: "结算电量",
    data: [
      6800, 7200, 6500, 8900, 9500, 10200, 11800, 10500, 9800, 8900, 7600, 7100
    ],
    type: "bar",
    barWidth: 15,
    itemStyle: {
      color: {
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        type: "linear",
        global: false,
        colorStops: [
          { offset: 0, color: "#0b9eff" },
          { offset: 1, color: "#63caff" }
        ]
      }
    },
    label: { show: false }
  },
  {
    data: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
    type: "pictorialBar",
    symbol: "diamond",
    symbolOffset: [0, "50%"],
    tooltip: { show: false },
    symbolSize: [15, 15]
  },
  {
    name: "结算电量",
    yAxisIndex: 0,
    data: [
      6800, 7200, 6500, 8900, 9500, 10200, 11800, 10500, 9800, 8900, 7600, 7100
    ],
    type: "pictorialBar",
    symbolPosition: "end",
    symbol: "diamond",
    symbolOffset: [0, "-50%"],
    symbolSize: [15, 12],
    zlevel: 2
  },
  {
    yAxisIndex: 0,
    data: [
      11800, 11800, 11800, 11800, 11800, 11800, 11800, 11800, 11800, 11800,
      11800, 11800
    ],
    type: "bar",
    barWidth: 15,
    tooltip: { show: false },
    barGap: "-100%",
    itemStyle: { color: "#1462BD", opacity: 0.2 },
    zlevel: -1
  },
  {
    data: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
    type: "pictorialBar",
    symbol: "diamond",
    tooltip: { show: false },
    symbolOffset: [0, "50%"],
    symbolSize: [15, 15],
    zlevel: -2
  },
  {
    yAxisIndex: 0,
    data: [
      11800, 11800, 11800, 11800, 11800, 11800, 11800, 11800, 11800, 11800,
      11800, 11800
    ],
    type: "pictorialBar",
    symbolPosition: "end",
    tooltip: { show: false },
    symbol: "diamond",
    itemStyle: { color: "#1462BD", opacity: 0.2 },
    symbolOffset: [0, "-50%"],
    symbolSize: [15, 12],
    zlevel: -1
  },
  {
    name: "结算电价",
    yAxisIndex: 1,
    data: [
      0.45, 0.48, 0.42, 0.52, 0.55, 0.58, 0.62, 0.59, 0.56, 0.51, 0.47, 0.44
    ],
    type: "line",
    itemStyle: { color: "#5EF4FF" },
    zlevel: 2
  }
]);

const settleLegend: EChartsOption["legend"] = [
  {
    name: "结算电量",
    textStyle: { color: "#EAF1FF" },
    itemStyle: { color: "#2762E3" },
    icon: "roundRect"
  },
  {
    name: "结算电价",
    textStyle: { color: "#EAF1FF" },
    itemStyle: { color: "#2762E3" }
  }
];

// 用户画像数据
const pieSeries = ref([
  {
    name: "用户画像分析",
    type: "pie",
    radius: ["40%", "70%"],
    label: {
      show: true,
      color: "#fff",
      formatter: "{b}  {c}",
      position: "outside"
    },
    minAngle: 20,
    avoidLabelOverlap: true,
    labelLine: { show: true },
    itemStyle: { borderColor: "#47A7C8", borderWidth: 1 },
    data: [
      { value: 320, name: "A类" },
      { value: 450, name: "B类" },
      { value: 280, name: "C类" },
      { value: 206, name: "D类" }
    ]
  }
]);

// 销售漏斗数据
const funnelSeries = ref([
  { name: "潜在客户", value: 1580, id: 1 },
  { name: "意向客户", value: 890, id: 2 },
  { name: "签约客户", value: 456, id: 3 },
  { name: "成交客户", value: 256, id: 4 }
]);

function setTime() {
  time.value = dayjs();
}

function toggle() {
  const element = document.getElementById("staticFullDiv");
  screenFull.toggle(element);
}

const { pause, resume } = useIntervalFn(setTime, 1000);

onMounted(() => {
  resume();
});

onUnmounted(() => {
  pause();
});
</script>

<style lang="scss" scoped>
.big-screen-box {
  width: 100%;
  height: 1080px;
  overflow: auto;
  box-sizing: border-box;
  position: relative;
  background: url("/src/assets/images/BigScreen/bg.png") no-repeat;
  background-size: 100% 100%;

  .main-content {
    width: 100%;
    height: 100%;
    padding: 100px 45px 20px 45px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .edge-module {
      width: 480px;
      flex-shrink: 0;

      & > div {
        background: url("/src/assets/images/BigScreen/module-bg.png");
        background-size: 100% 100%;
        backdrop-filter: blur(5px);
        width: 100%;
        height: 304px;
        margin-top: 15px;

        .title-box {
          display: flex;
          height: 38px;
          align-items: center;
          justify-content: space-between;
          padding-top: 14px;
          padding-left: 38px;

          .title {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            text-shadow: 0px 0px 12px rgba(193, 227, 255, 0.93);
          }

          .value {
            padding-right: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 800;
            font-size: 14px;
            color: #ffffff;
            text-shadow: 0px 0px 12px rgba(193, 227, 255, 0.53);
          }
        }

        &:first-child {
          margin-top: 0;
        }
      }

      .left-module1 {
        .module-content {
          margin: 28px 25px 0px 25px;
          height: 223px;
          box-sizing: border-box;
          padding: 5px;
          background: rgba(108, 184, 255, 0.08);
          border: 0px solid rgba(110, 193, 247, 0.32);
          overflow: hidden;

          .box {
            height: 210px;
            padding: 8px;
            overflow: hidden;
            background: linear-gradient(
              180deg,
              rgba(88, 209, 255, 0.13) 0%,
              rgba(81, 192, 255, 0.16) 100%
            );
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #a7d3ff;
          }
        }
      }

      .right-module1 {
        .content {
          margin: 20px;
          height: 223px;
          box-sizing: border-box;
          padding: 5px;
          overflow-y: hidden;

          .li {
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(108, 184, 255, 0.08);
            border: 0px solid rgba(110, 193, 247, 0.32);
            padding: 5px 8px;
            margin-top: 10px;

            .name {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #85b7ff;
              width: 70%;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              padding: 5px;
              background: linear-gradient(
                180deg,
                rgba(88, 169, 255, 0.13) 0%,
                rgba(81, 159, 255, 0.16) 100%
              );
            }

            .value {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
            }
          }
        }
      }

      .right-module2 {
        .chartsPie-box {
          background: url("/src/assets/images/BigScreen/pie-bg.png") no-repeat;
          background-size: 70px 70px;
          background-position: center center;
        }
      }

      .right-module3 {
        .funnel-box {
          position: relative;
          display: flex;
          margin-left: 40px;
          margin-top: 50px;

          .legend-box {
            position: absolute;
            top: -5px;
            right: 40px;
            color: #fff;
            font-size: 14px;

            .content {
              display: flex;
              flex-direction: column;
              align-items: center;
              margin-top: 10px;
              cursor: pointer;

              &:first-child {
                margin-top: 0;
              }

              .value {
                color: #59d7ff;
                font-size: 12px;
              }
            }
          }
        }
      }
    }

    .center-module {
      width: 655px;
      flex-shrink: 0;
      padding: 0 15px;

      .center-card {
        .card-wrapper {
          display: flex;
          justify-content: space-around;

          .card-box {
            margin-right: 15px;
            width: 33.33%;
            height: 102px;
            background: url("/src/assets/images/BigScreen/card-bg.png")
              no-repeat;
            background-size: 100% 100%;
            backdrop-filter: blur(5px);
            color: #ffffff;

            .card-title {
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              text-shadow: 0px 0px 12px rgba(193, 227, 255, 0.93);
              text-align: center;
              padding-top: 4px;
            }

            .card-value {
              display: flex;
              justify-content: center;
              align-items: baseline;
              line-height: 74px;

              .value {
                font-size: 31px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                color: #5bddff;
              }
            }

            &:last-child {
              margin-right: 0;
            }
          }
        }
      }

      .center-content {
        position: relative;

        .chart-box {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 290px;
          height: 300px;
        }
      }
    }
  }

  .top-title {
    width: 100%;
    height: 80px;
    position: absolute;
    top: 0;
    z-index: 1;

    img {
      height: 80px;
    }

    .title-bg {
      width: 100%;
    }

    .title-box {
      position: absolute;
      top: 14%;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      align-items: center;
      cursor: pointer;

      .logo {
        margin-top: 4px;
        width: 48px;
        height: 48px;
      }

      .title {
        margin-left: 10px;
        font-size: 18px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        color: #ffffff;
        line-height: 52px;
        letter-spacing: 2px;
        background: linear-gradient(
          180deg,
          #3da0ff 0%,
          #e6fdff 50%,
          #68b1ff 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  .time {
    position: absolute;
    top: 16px;
    right: 20px;
    color: #a7d3ff;
  }

  .left {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 48px;
    height: 90%;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .bottom {
    width: 55%;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);

    img {
      width: 100%;
    }
  }

  .right {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 48px;
    height: 90%;

    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
