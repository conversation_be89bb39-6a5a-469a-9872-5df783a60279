// 布局配置管理
export interface LayoutConfig {
  // 头部配置
  header: {
    show: boolean;           // 是否显示头部
    showLogo: boolean;       // 是否显示Logo
    showBreadcrumb: boolean; // 是否显示面包屑
    showUserInfo: boolean;   // 是否显示用户信息
    showFullscreen: boolean; // 是否显示全屏按钮
    showNotice: boolean;     // 是否显示通知
    showSettings: boolean;   // 是否显示设置
  };
  
  // 侧边栏配置
  sidebar: {
    show: boolean;           // 是否显示侧边栏
    showLogo: boolean;       // 是否显示侧边栏Logo
    collapsible: boolean;    // 是否可折叠
    defaultCollapsed: boolean; // 默认是否折叠
  };
  
  // 标签页配置
  tabs: {
    show: boolean;           // 是否显示标签页
    showIcon: boolean;       // 是否显示图标
    showClose: boolean;      // 是否显示关闭按钮
    showRefresh: boolean;    // 是否显示刷新按钮
  };
  
  // 页脚配置
  footer: {
    show: boolean;           // 是否显示页脚
    text: string;            // 页脚文本
  };
  
  // 嵌入模式配置
  embed: {
    mode: 'normal' | 'iframe' | 'minimal'; // 显示模式
    hideAll: boolean;        // 隐藏所有UI元素（仅显示内容）
    allowedOrigins: string[]; // 允许嵌入的域名
  };
}

// 默认配置
export const defaultLayoutConfig: LayoutConfig = {
  header: {
    show: true,
    showLogo: true,
    showBreadcrumb: true,
    showUserInfo: true,
    showFullscreen: true,
    showNotice: true,
    showSettings: true,
  },
  sidebar: {
    show: true,
    showLogo: true,
    collapsible: true,
    defaultCollapsed: false,
  },
  tabs: {
    show: true,
    showIcon: true,
    showClose: true,
    showRefresh: true,
  },
  footer: {
    show: true,
    text: '© 2024 智慧售电平台',
  },
  embed: {
    mode: 'normal',
    hideAll: false,
    allowedOrigins: ['*'],
  },
};

// 嵌入模式预设配置
export const embedPresets: Record<string, Partial<LayoutConfig>> = {
  // 完全隐藏模式 - 仅显示页面内容
  minimal: {
    header: { show: false },
    sidebar: { show: false },
    tabs: { show: false },
    footer: { show: false },
    embed: { mode: 'minimal', hideAll: true },
  },
  
  // iframe模式 - 隐藏头部和侧边栏
  iframe: {
    header: { show: false },
    sidebar: { show: false },
    tabs: { show: false },
    footer: { show: false },
    embed: { mode: 'iframe', hideAll: false },
  },
  
  // 简化模式 - 保留基本导航
  simple: {
    header: {
      show: true,
      showLogo: false,
      showBreadcrumb: true,
      showUserInfo: false,
      showFullscreen: false,
      showNotice: false,
      showSettings: false,
    },
    sidebar: {
      show: true,
      showLogo: false,
      collapsible: false,
      defaultCollapsed: false,
    },
    tabs: { show: false },
    footer: { show: false },
    embed: { mode: 'normal', hideAll: false },
  },
  
  // 只显示内容区域
  contentOnly: {
    header: { show: false },
    sidebar: { show: false },
    tabs: { show: false },
    footer: { show: false },
    embed: { mode: 'minimal', hideAll: true },
  },
};

// 布局配置管理类
export class LayoutConfigManager {
  private config: LayoutConfig;
  private listeners: Array<(config: LayoutConfig) => void> = [];

  constructor(initialConfig: LayoutConfig = defaultLayoutConfig) {
    this.config = { ...initialConfig };
    this.initFromUrl();
  }

  // 从URL参数初始化配置
  private initFromUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    
    // 检查嵌入模式
    const embedMode = urlParams.get('embed');
    if (embedMode && embedMode in embedPresets) {
      this.applyPreset(embedMode);
    }
    
    // 检查具体的显示/隐藏参数
    const hideHeader = urlParams.get('hideHeader') === 'true';
    const hideSidebar = urlParams.get('hideSidebar') === 'true';
    const hideTabs = urlParams.get('hideTabs') === 'true';
    const hideFooter = urlParams.get('hideFooter') === 'true';
    const hideAll = urlParams.get('hideAll') === 'true';
    
    if (hideAll) {
      this.applyPreset('minimal');
    } else {
      if (hideHeader) this.config.header.show = false;
      if (hideSidebar) this.config.sidebar.show = false;
      if (hideTabs) this.config.tabs.show = false;
      if (hideFooter) this.config.footer.show = false;
    }
  }

  // 获取当前配置
  getConfig(): LayoutConfig {
    return { ...this.config };
  }

  // 更新配置
  updateConfig(newConfig: Partial<LayoutConfig>) {
    this.config = { ...this.config, ...newConfig };
    this.notifyListeners();
  }

  // 应用预设配置
  applyPreset(presetName: string) {
    if (presetName in embedPresets) {
      const preset = embedPresets[presetName];
      this.config = this.mergeConfig(this.config, preset);
      this.notifyListeners();
    }
  }

  // 深度合并配置
  private mergeConfig(base: LayoutConfig, override: Partial<LayoutConfig>): LayoutConfig {
    const result = { ...base };
    
    Object.keys(override).forEach(key => {
      if (typeof override[key] === 'object' && override[key] !== null) {
        result[key] = { ...result[key], ...override[key] };
      } else {
        result[key] = override[key];
      }
    });
    
    return result;
  }

  // 添加配置变更监听器
  addListener(listener: (config: LayoutConfig) => void) {
    this.listeners.push(listener);
  }

  // 移除监听器
  removeListener(listener: (config: LayoutConfig) => void) {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  // 通知所有监听器
  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.config));
  }

  // 检查是否为嵌入模式
  isEmbedMode(): boolean {
    return this.config.embed.mode !== 'normal';
  }

  // 检查是否隐藏所有UI
  isMinimalMode(): boolean {
    return this.config.embed.hideAll;
  }

  // 生成嵌入URL
  generateEmbedUrl(basePath: string, options: {
    preset?: string;
    hideHeader?: boolean;
    hideSidebar?: boolean;
    hideTabs?: boolean;
    hideFooter?: boolean;
    hideAll?: boolean;
  } = {}): string {
    const url = new URL(basePath, window.location.origin);
    
    if (options.preset) {
      url.searchParams.set('embed', options.preset);
    } else {
      if (options.hideHeader) url.searchParams.set('hideHeader', 'true');
      if (options.hideSidebar) url.searchParams.set('hideSidebar', 'true');
      if (options.hideTabs) url.searchParams.set('hideTabs', 'true');
      if (options.hideFooter) url.searchParams.set('hideFooter', 'true');
      if (options.hideAll) url.searchParams.set('hideAll', 'true');
    }
    
    return url.toString();
  }
}

// 全局布局配置管理器实例
export const layoutConfigManager = new LayoutConfigManager();
