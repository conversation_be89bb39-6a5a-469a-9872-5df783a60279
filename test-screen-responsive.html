<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <script type="text/javascript" src="/resource/map/mapJson.js"></script>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>大屏响应式测试</title>
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: Arial, sans-serif;
      }
      
      .test-controls {
        position: fixed;
        top: 10px;
        left: 10px;
        z-index: 9999;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px;
        border-radius: 5px;
        font-size: 12px;
      }
      
      .test-controls button {
        margin: 2px;
        padding: 5px 10px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
      }
      
      .test-controls button:hover {
        background: #0056b3;
      }
      
      #test-screen-app {
        width: 100vw;
        height: 100vh;
      }
      
      .size-info {
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 9999;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px;
        border-radius: 5px;
        font-size: 12px;
      }
    </style>
    <script>
      window.process = {};
      
      function updateSizeInfo() {
        const info = document.getElementById('size-info');
        if (info) {
          info.innerHTML = `
            窗口: ${window.innerWidth} x ${window.innerHeight}<br>
            屏幕: ${screen.width} x ${screen.height}<br>
            设备像素比: ${window.devicePixelRatio}
          `;
        }
      }
      
      function testResize(width, height) {
        window.resizeTo(width, height);
        setTimeout(updateSizeInfo, 100);
      }
      
      window.addEventListener('resize', updateSizeInfo);
      window.addEventListener('load', updateSizeInfo);
    </script>
  </head>
  <body>
    <div class="test-controls">
      <div>大屏响应式测试</div>
      <button onclick="testResize(1920, 1080)">1920x1080</button>
      <button onclick="testResize(1366, 768)">1366x768</button>
      <button onclick="testResize(1280, 720)">1280x720</button>
      <button onclick="testResize(1024, 768)">1024x768</button>
      <button onclick="window.location.reload()">刷新</button>
    </div>
    
    <div class="size-info" id="size-info"></div>
    
    <div id="test-screen-app"></div>
    <script type="module" src="/src/static-screen-main.ts"></script>
  </body>
</html>
