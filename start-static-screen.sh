#!/bin/bash

echo "🚀 启动静态大屏开发服务器..."

# 检查依赖
if ! command -v pnpm &> /dev/null; then
    echo "❌ pnpm 未安装，请先安装 pnpm"
    echo "   npm install -g pnpm"
    exit 1
fi

# 检查node_modules
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    pnpm install
fi

echo "🔧 启动开发服务器..."
echo ""
echo "📺 静态大屏访问地址: http://localhost:8080/static-screen.html"
echo "🌐 主应用访问地址: http://localhost:8080/"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

# 启动开发服务器
pnpm dev
