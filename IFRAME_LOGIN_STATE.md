# iframe嵌套登录状态管理说明

## 问题概述

**问题**: iframe嵌套的框架在一个页面登录后再进入其他菜单页面时登录状态会丢失吗？

**答案**: **通常不会丢失，但需要正确的实现方式**

## 登录状态保持机制

### 1. 同域名情况下
如果iframe嵌套的页面与父页面在同一个域名下：
- ✅ **登录状态会保持** - Cookie和Session会正常共享
- ✅ **localStorage/sessionStorage会共享** - 可以正常访问
- ✅ **路由跳转正常** - 在iframe内部的路由跳转会保持登录状态

### 2. 跨域名情况下
如果iframe嵌套的页面与父页面不在同一个域名下：
- ❌ **Cookie不会共享** - 需要特殊处理
- ❌ **localStorage不会共享** - 需要通过postMessage通信
- ⚠️ **需要特殊的登录状态管理机制**

## 实现方案

### 方案一：Token传递方式（推荐）

#### 1. URL参数传递Token
```javascript
// 父页面传递token
const token = localStorage.getItem('access_token');
const iframeUrl = `https://your-domain.com/customer-record/index?token=${token}`;

// iframe页面接收token
const urlParams = new URLSearchParams(window.location.search);
const token = urlParams.get('token');
if (token) {
  localStorage.setItem('access_token', token);
  // 设置axios默认header
  axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
}
```

#### 2. postMessage通信方式
```javascript
// 父页面发送token
window.addEventListener('load', () => {
  const iframe = document.getElementById('embedded-iframe');
  const token = localStorage.getItem('access_token');
  iframe.contentWindow.postMessage({
    type: 'AUTH_TOKEN',
    token: token
  }, 'https://your-domain.com');
});

// iframe页面接收token
window.addEventListener('message', (event) => {
  if (event.origin !== 'https://parent-domain.com') return;
  
  if (event.data.type === 'AUTH_TOKEN') {
    const token = event.data.token;
    localStorage.setItem('access_token', token);
    // 设置认证header
    setAuthToken(token);
  }
});
```

### 方案二：SSO单点登录

#### 1. 实现统一认证中心
```javascript
// 认证中心验证
const validateToken = async (token) => {
  try {
    const response = await fetch('/api/auth/validate', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    return response.ok;
  } catch (error) {
    return false;
  }
};

// 路由守卫中验证
router.beforeEach(async (to, from, next) => {
  const token = getTokenFromUrl() || localStorage.getItem('access_token');
  
  if (token && await validateToken(token)) {
    next();
  } else {
    // 重定向到登录页面或请求新token
    requestNewToken();
  }
});
```

### 方案三：Session共享

#### 1. 配置Cookie域名
```javascript
// 设置Cookie为顶级域名
document.cookie = `session_id=${sessionId}; domain=.your-domain.com; path=/`;
```

#### 2. 服务端Session配置
```javascript
// Express.js示例
app.use(session({
  secret: 'your-secret',
  cookie: {
    domain: '.your-domain.com',  // 设置为顶级域名
    httpOnly: true,
    secure: true,  // HTTPS环境
    sameSite: 'none'  // 跨站点Cookie
  }
}));
```

## 具体实现代码

### 1. 创建认证工具类

```typescript
// src/utils/iframeAuth.ts
export class IframeAuthManager {
  private token: string | null = null;
  private refreshTimer: number | null = null;

  constructor() {
    this.initAuth();
  }

  // 初始化认证
  private initAuth() {
    // 从URL参数获取token
    const urlToken = this.getTokenFromUrl();
    if (urlToken) {
      this.setToken(urlToken);
      // 清除URL中的token参数
      this.clearTokenFromUrl();
    }

    // 从localStorage获取token
    const storedToken = localStorage.getItem('access_token');
    if (storedToken) {
      this.setToken(storedToken);
    }

    // 监听父页面消息
    this.listenForParentMessages();
  }

  // 从URL获取token
  private getTokenFromUrl(): string | null {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('token');
  }

  // 清除URL中的token参数
  private clearTokenFromUrl() {
    const url = new URL(window.location.href);
    url.searchParams.delete('token');
    window.history.replaceState({}, document.title, url.toString());
  }

  // 设置token
  setToken(token: string) {
    this.token = token;
    localStorage.setItem('access_token', token);
    
    // 设置axios默认header
    if (window.axios) {
      window.axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    }

    // 启动token刷新定时器
    this.startTokenRefresh();
  }

  // 获取token
  getToken(): string | null {
    return this.token || localStorage.getItem('access_token');
  }

  // 监听父页面消息
  private listenForParentMessages() {
    window.addEventListener('message', (event) => {
      // 验证来源（根据实际情况修改）
      if (!this.isValidOrigin(event.origin)) return;

      if (event.data.type === 'AUTH_TOKEN') {
        this.setToken(event.data.token);
      } else if (event.data.type === 'LOGOUT') {
        this.logout();
      }
    });
  }

  // 验证消息来源
  private isValidOrigin(origin: string): boolean {
    const allowedOrigins = [
      'https://your-parent-domain.com',
      'http://localhost:8080',
      // 添加其他允许的域名
    ];
    return allowedOrigins.includes(origin);
  }

  // 启动token刷新
  private startTokenRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }

    // 每30分钟检查一次token有效性
    this.refreshTimer = window.setInterval(() => {
      this.validateAndRefreshToken();
    }, 30 * 60 * 1000);
  }

  // 验证并刷新token
  private async validateAndRefreshToken() {
    const token = this.getToken();
    if (!token) return;

    try {
      const response = await fetch('/api/auth/validate', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (!response.ok) {
        // token无效，尝试刷新
        await this.refreshToken();
      }
    } catch (error) {
      console.error('Token validation failed:', error);
      this.logout();
    }
  }

  // 刷新token
  private async refreshToken() {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${this.getToken()}` }
      });

      if (response.ok) {
        const data = await response.json();
        this.setToken(data.access_token);
      } else {
        this.logout();
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      this.logout();
    }
  }

  // 登出
  logout() {
    this.token = null;
    localStorage.removeItem('access_token');
    
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }

    // 通知父页面登出
    if (window.parent !== window) {
      window.parent.postMessage({ type: 'IFRAME_LOGOUT' }, '*');
    }

    // 重定向到登录页面或显示登录提示
    this.handleLogout();
  }

  // 处理登出后的操作
  private handleLogout() {
    // 可以重定向到登录页面或显示登录模态框
    window.location.href = '/login';
  }
}

// 全局实例
export const iframeAuth = new IframeAuthManager();
```

### 2. 在Vue应用中使用

```typescript
// src/main.ts
import { iframeAuth } from '@/utils/iframeAuth';

// 在应用启动时初始化
const app = createApp(App);

// 设置axios拦截器
axios.interceptors.request.use((config) => {
  const token = iframeAuth.getToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 响应拦截器处理401错误
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      iframeAuth.logout();
    }
    return Promise.reject(error);
  }
);

app.mount('#app');
```

### 3. 路由守卫

```typescript
// src/router/index.ts
import { iframeAuth } from '@/utils/iframeAuth';

router.beforeEach((to, from, next) => {
  const token = iframeAuth.getToken();
  
  if (to.path !== '/login' && !token) {
    // 没有token，重定向到登录页面
    next('/login');
  } else {
    next();
  }
});
```

## 最佳实践建议

### 1. 安全性考虑
- ✅ 使用HTTPS协议
- ✅ 验证消息来源（origin）
- ✅ 设置合理的token过期时间
- ✅ 实现token自动刷新机制

### 2. 用户体验
- ✅ 提供登录状态指示器
- ✅ 自动处理token过期
- ✅ 优雅的错误处理
- ✅ 无缝的页面跳转

### 3. 开发调试
- ✅ 添加详细的日志记录
- ✅ 提供开发环境的调试工具
- ✅ 模拟不同的登录状态场景

## 总结

iframe嵌套的登录状态管理关键在于：

1. **正确的token传递机制** - URL参数或postMessage
2. **统一的认证验证** - 服务端token验证
3. **自动的状态同步** - 父子页面状态同步
4. **优雅的错误处理** - 登录失效时的处理

通过以上方案，可以确保iframe嵌套的页面在不同菜单间跳转时保持登录状态。
