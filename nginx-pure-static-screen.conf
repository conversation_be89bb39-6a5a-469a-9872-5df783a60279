# 纯静态大屏配置（推荐）- 只能访问静态大屏
server {
    listen 8081;  # 独立端口
    server_name localhost;
    
    # 静态资源根目录
    root /usr/share/nginx/html;
    index static-screen.html;
    
    # 所有请求都指向静态大屏
    location / {
        try_files /static-screen.html =404;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
    
    # 地图数据文件
    location /resource/ {
        expires 1d;
        add_header Cache-Control "public";
        try_files $uri =404;
    }
    
    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
    }
    
    # 禁止访问其他HTML文件
    location ~* \.html$ {
        if ($uri != "/static-screen.html") {
            return 403;
        }
        try_files $uri =404;
    }
    
    # 错误页面都指向静态大屏
    error_page 403 404 500 502 503 504 /static-screen.html;
}

# 混合模式配置 - 可以访问其他页面但默认是静态大屏
server {
    listen 8082;  # 另一个端口
    server_name localhost;
    
    # 静态资源根目录
    root /usr/share/nginx/html;
    index static-screen.html;
    
    # 根路径直接访问静态大屏
    location = / {
        try_files /static-screen.html =404;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # 允许访问其他页面
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
    
    # 地图数据文件
    location /resource/ {
        expires 1d;
        add_header Cache-Control "public";
        try_files $uri =404;
    }
    
    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
    }
    
    # 错误页面
    error_page 404 /index.html;
    error_page 500 502 503 504 /static-screen.html;
}
