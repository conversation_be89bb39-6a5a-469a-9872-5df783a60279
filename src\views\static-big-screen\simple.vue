<template>
  <section id="staticFullDiv" class="big-screen-box" style="margin: 0">
    <div class="top-title">
      <img
        class="title-bg"
        src="/src/assets/images/BigScreen/title-bg.png"
        alt=""
      />
      <div class="title-box" @click="toggle">
        <img
          class="logo"
          src="/src/assets/images/BigScreen/stateGrid.png"
          alt=""
        />
        <div class="title">国网江西综合能源服务有限公司-智慧售电平台</div>
      </div>
    </div>
    <!-- 右侧时间 -->
    <div class="time">{{ currentTime }}</div>
    <div class="left">
      <img src="/src/assets/images/BigScreen/left-bg.png" alt="" />
    </div>
    <div class="bottom">
      <img src="/src/assets/images/BigScreen/bottom-bg.png" alt="" />
    </div>
    <div class="right">
      <img src="/src/assets/images/BigScreen/right-bg.png" alt="" />
    </div>
    <!-- 主题内容部分 -->
    <div class="main-content">
      <div class="edge-module">
        <div class="left-module1">
          <div class="title-box">
            <div class="title">公司简介</div>
          </div>
          <div class="module-content">
            <div class="box">
              <div class="company-intro">
                <p>
                  江西赣能能源服务有限公司（以下简称"赣能能源"）于2019年5月23日，由江西赣能股份有限公司、江西省天然气集团有限公司和中国电建江西省电力设计院有限公司共同出资设立。
                </p>
                <p>
                  公司致力于打造成为集发、售、配电及油、气、节能服务于一体的综合能源服务商。从售电业务起步，逐步涉入配电，增值服务及综合能源利用领域。
                </p>
                <p>
                  秉承"赣能能源，您的能源贴心管家"的服务理念，为用户提供一站式的全方位、多元化综合能源及个性化增值服务。
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="left-module2">
          <div class="title-box">
            <div class="title">收益概览</div>
            <div class="value pb-[6px]">年累计：12,580万元</div>
          </div>
          <div class="mt-[30px]">
            <div class="chart-container">
              <div class="chart-title">月度收益趋势</div>
              <div class="bar-chart">
                <div
                  v-for="(value, index) in revenueData"
                  :key="index"
                  class="bar-item"
                >
                  <div
                    class="bar"
                    :style="{ height: (value / maxRevenue) * 100 + '%' }"
                  ></div>
                  <div class="bar-label">{{ index + 7 }}月</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="left-module3">
          <div class="title-box">
            <div class="title">结算管理</div>
            <div class="value pb-[6px]">年累计：76,890MWh</div>
          </div>
          <div class="mt-[30px]">
            <div class="chart-container">
              <div class="chart-title">月度结算趋势</div>
              <div class="bar-chart">
                <div
                  v-for="(value, index) in settlementData"
                  :key="index"
                  class="bar-item"
                >
                  <div
                    class="bar settlement-bar"
                    :style="{ height: (value / maxSettlement) * 100 + '%' }"
                  ></div>
                  <div class="bar-label">{{ index + 7 }}月</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="center-module">
        <div class="center-card">
          <div class="card-wrapper">
            <div class="card-box">
              <div class="card-title">签约用户</div>
              <div class="card-value">
                <div class="value">1,256</div>
                <div class="unit">家</div>
              </div>
            </div>
            <div class="card-box">
              <div class="card-title">目标电量</div>
              <div class="card-value">
                <div class="value">85,420</div>
                <div class="unit">MWh</div>
              </div>
            </div>
          </div>
          <div class="card-wrapper mt-[15px]">
            <div class="card-box">
              <div class="card-title">合同电量</div>
              <div class="card-value">
                <div class="value">78,650</div>
                <div class="unit">MWh</div>
              </div>
            </div>
            <div class="card-box">
              <div class="card-title">结算电量</div>
              <div class="card-value">
                <div class="value">76,890</div>
                <div class="unit">MWh</div>
              </div>
            </div>
            <div class="card-box">
              <div class="card-title">完成率</div>
              <div class="card-value">
                <div class="value">92.5</div>
                <div class="unit">%</div>
              </div>
            </div>
          </div>
        </div>
        <div class="map-section">
          <div class="map-placeholder">
            <div class="map-title">江西省电力分布图</div>
            <div class="city-grid">
              <div v-for="city in cities" :key="city" class="city-item">
                {{ city }}
              </div>
            </div>
          </div>
        </div>
        <div class="center-content">
          <div class="chart-box">
            <div class="area-chart">
              <div class="chart-title">地区用户统计</div>
              <div class="area-bars">
                <div
                  v-for="item in areaData"
                  :key="item.name"
                  class="area-bar-item"
                >
                  <div
                    class="area-bar"
                    :style="{
                      height: (item.value / maxAreaValue) * 150 + 'px'
                    }"
                  ></div>
                  <div class="area-name">{{ item.name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="edge-module">
        <div class="right-module1">
          <div class="title-box">
            <div class="title">代理大用户合同电量(MWh)</div>
          </div>
          <div class="user-list">
            <div v-for="user in bigUsers" :key="user.name" class="user-item">
              <div class="user-name">{{ user.name }}</div>
              <div class="user-value">{{ user.value }}</div>
            </div>
          </div>
        </div>
        <div class="right-module2">
          <div class="title-box">
            <div class="title">用户画像分析</div>
          </div>
          <div class="mt-[30px]">
            <div class="pie-chart">
              <div class="chart-title">用户分类统计</div>
              <div class="pie-legend">
                <div
                  v-for="item in userTypes"
                  :key="item.name"
                  class="legend-item"
                >
                  <div
                    class="legend-color"
                    :style="{ background: item.color }"
                  ></div>
                  <span>{{ item.name }}: {{ item.value }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="right-module3">
          <div class="title-box">
            <div class="title">销售漏斗</div>
          </div>
          <div class="funnel-box">
            <img
              src="/src/assets/images/BigScreen/funel.png"
              class="funnel-img"
              alt=""
            />
            <div class="legend-box">
              <div
                v-for="item in funnelData"
                :key="item.name"
                class="funnel-item"
              >
                <div class="label">{{ item.name }}</div>
                <div class="value">({{ item.value }})</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";

defineOptions({
  name: "SimpleStaticBigScreen"
});

// 时间显示
const currentTime = ref("");

// 静态数据
const revenueData = [850, 920, 1100, 1250, 1380, 1520];
const maxRevenue = Math.max(...revenueData);

const settlementData = [6800, 7200, 8900, 9500, 10200, 11800];
const maxSettlement = Math.max(...settlementData);

const cities = [
  "南昌市",
  "九江市",
  "景德镇市",
  "萍乡市",
  "新余市",
  "鹰潭市",
  "赣州市",
  "宜春市",
  "上饶市",
  "吉安市",
  "抚州市"
];

const areaData = [
  { name: "南昌", value: 1250 },
  { name: "九江", value: 980 },
  { name: "景德镇", value: 750 },
  { name: "萍乡", value: 650 },
  { name: "新余", value: 580 },
  { name: "鹰潭", value: 520 },
  { name: "赣州", value: 1100 },
  { name: "宜春", value: 890 },
  { name: "上饶", value: 720 },
  { name: "吉安", value: 680 },
  { name: "抚州", value: 590 }
];
const maxAreaValue = Math.max(...areaData.map(item => item.value));

const bigUsers = [
  { name: "江西铜业集团有限公司", value: "15,680" },
  { name: "新钢集团有限公司", value: "12,450" },
  { name: "江西方大钢铁集团", value: "11,230" },
  { name: "江西晨鸣纸业有限公司", value: "9,870" },
  { name: "江西理文化工有限公司", value: "8,650" },
  { name: "江西赛维LDK太阳能", value: "7,890" },
  { name: "江西洪都航空工业集团", value: "6,780" },
  { name: "江西昌河汽车有限责任公司", value: "5,920" }
];

const userTypes = [
  { name: "A类", value: 320, color: "#0d714b" },
  { name: "B类", value: 450, color: "#1189a4" },
  { name: "C类", value: 280, color: "#dc8018" },
  { name: "D类", value: 206, color: "#c2aa38" }
];

const funnelData = [
  { name: "报价", value: 1580 },
  { name: "已签约", value: 890 },
  { name: "未签约", value: 456 },
  { name: "已成交", value: 256 }
];

// 更新时间
function updateTime() {
  const now = new Date();
  currentTime.value = now.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit"
  });
}

// 全屏切换
function toggle() {
  const element = document.getElementById("staticFullDiv");
  if (element) {
    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      element.requestFullscreen();
    }
  }
}

let timeInterval: NodeJS.Timeout;

onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});
</script>

<style lang="scss" scoped>
.big-screen-box {
  width: 100%;
  min-height: 1080px;
  height: auto;
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  position: relative;
  background: url("/src/assets/images/BigScreen/bg.png") no-repeat;
  background-size: 100% 100%;
  background-attachment: fixed;

  // 非全屏状态下的适配
  @media screen and (max-height: 1080px) {
    min-height: 100vh;
    height: auto;
    overflow-y: auto;

    .main-content {
      padding: 80px 45px 20px 45px;
    }
  }

  // 小屏幕适配
  @media screen and (max-width: 1920px) {
    .main-content {
      .edge-module {
        width: 420px;

        & > div {
          height: 280px;
        }
      }

      .center-module {
        width: 580px;
      }
    }
  }

  .main-content {
    width: 100%;
    height: 100%;
    padding: 100px 45px 20px 45px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .edge-module {
      width: 480px;
      flex-shrink: 0;

      & > div {
        background: url("/src/assets/images/BigScreen/module-bg.png");
        background-size: 100% 100%;
        backdrop-filter: blur(5px);
        width: 100%;
        height: 304px;
        margin-top: 15px;

        .title-box {
          display: flex;
          height: 38px;
          align-items: center;
          justify-content: space-between;
          padding-top: 14px;
          padding-left: 38px;

          .title {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            text-shadow: 0px 0px 12px rgba(193, 227, 255, 0.93);
          }

          .value {
            padding-right: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 800;
            font-size: 14px;
            color: #ffffff;
            text-shadow: 0px 0px 12px rgba(193, 227, 255, 0.53);
          }
        }

        &:first-child {
          margin-top: 0;
        }
      }

      .left-module1 {
        .module-content {
          margin: 28px 25px 0px 25px;
          height: 223px;
          box-sizing: border-box;
          padding: 5px;
          background: rgba(108, 184, 255, 0.08);
          border: 0px solid rgba(110, 193, 247, 0.32);
          overflow: hidden;

          .box {
            height: 210px;
            padding: 8px;
            overflow-y: auto;
            background: linear-gradient(
              180deg,
              rgba(88, 209, 255, 0.13) 0%,
              rgba(81, 192, 255, 0.16) 100%
            );
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #a7d3ff;

            .company-intro p {
              margin: 10px 0;
              text-indent: 2em;
              line-height: 1.5;
            }
          }
        }
      }

      .chart-container {
        padding: 20px;
        height: 200px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .chart-title {
          color: #a7d3ff;
          font-size: 16px;
          margin-bottom: 15px;
        }

        .bar-chart {
          display: flex;
          align-items: end;
          justify-content: space-around;
          width: 100%;
          height: 120px;

          .bar-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;

            .bar {
              width: 20px;
              background: linear-gradient(to top, #0b9eff, #63caff);
              border-radius: 2px;
              margin-bottom: 5px;
              min-height: 10px;
            }

            .settlement-bar {
              background: linear-gradient(to top, #5ef4ff, #0b9eff);
            }

            .bar-label {
              font-size: 12px;
              color: #a7d3ff;
            }
          }
        }
      }

      .right-module1 {
        .user-list {
          margin: 20px;
          height: 223px;
          box-sizing: border-box;
          padding: 5px;
          overflow-y: auto;

          .user-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(108, 184, 255, 0.08);
            border: 0px solid rgba(110, 193, 247, 0.32);
            padding: 8px;
            margin-bottom: 8px;
            border-radius: 4px;

            .user-name {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #85b7ff;
              width: 70%;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .user-value {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
            }
          }
        }
      }

      .right-module2 {
        .pie-chart {
          padding: 20px;
          text-align: center;

          .chart-title {
            color: #a7d3ff;
            font-size: 16px;
            margin-bottom: 15px;
          }

          .pie-legend {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;

            .legend-item {
              display: flex;
              align-items: center;
              font-size: 14px;
              color: #a7d3ff;

              .legend-color {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                margin-right: 8px;
              }
            }
          }
        }
      }

      .right-module3 {
        .funnel-box {
          position: relative;
          display: flex;
          margin-left: 40px;
          margin-top: 50px;

          .funnel-img {
            height: 170px;
            width: 320px;
          }

          .legend-box {
            position: absolute;
            top: -5px;
            right: 40px;
            color: #fff;
            font-size: 14px;

            .funnel-item {
              display: flex;
              flex-direction: column;
              align-items: center;
              margin-top: 10px;
              cursor: pointer;

              &:first-child {
                margin-top: 0;
              }

              .value {
                color: #59d7ff;
                font-size: 12px;
              }
            }
          }
        }
      }
    }

    .center-module {
      width: 655px;
      flex-shrink: 0;
      padding: 0 15px;

      .center-card {
        .card-wrapper {
          display: flex;
          justify-content: space-around;

          .card-box {
            margin-right: 15px;
            width: 33.33%;
            height: 102px;
            background: url("/src/assets/images/BigScreen/card-bg.png")
              no-repeat;
            background-size: 100% 100%;
            backdrop-filter: blur(5px);
            color: #ffffff;

            .card-title {
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              text-shadow: 0px 0px 12px rgba(193, 227, 255, 0.93);
              text-align: center;
              padding-top: 4px;
            }

            .card-value {
              display: flex;
              justify-content: center;
              align-items: baseline;
              line-height: 74px;

              .value {
                font-size: 31px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                color: #5bddff;
              }
            }

            &:last-child {
              margin-right: 0;
            }
          }
        }
      }

      .map-section {
        margin: 20px 0;

        .map-placeholder {
          height: 700px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background: rgba(108, 184, 255, 0.08);
          border-radius: 8px;
          padding: 20px;

          .map-title {
            font-size: 24px;
            color: #a7d3ff;
            margin-bottom: 30px;
          }

          .city-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            max-width: 500px;

            .city-item {
              padding: 10px;
              background: rgba(88, 169, 255, 0.13);
              border-radius: 4px;
              font-size: 14px;
              color: #a7d3ff;
              text-align: center;
            }
          }
        }
      }

      .center-content {
        position: relative;

        .chart-box {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 290px;
          height: 300px;

          .area-chart {
            height: 100%;
            padding: 20px;
            background: rgba(108, 184, 255, 0.08);
            border-radius: 8px;
            display: flex;
            flex-direction: column;

            .chart-title {
              color: #a7d3ff;
              font-size: 16px;
              margin-bottom: 15px;
              text-align: center;
            }

            .area-bars {
              display: flex;
              align-items: end;
              justify-content: space-around;
              flex: 1;
              padding-bottom: 20px;

              .area-bar-item {
                display: flex;
                flex-direction: column;
                align-items: center;

                .area-bar {
                  width: 12px;
                  background: linear-gradient(to top, #0b9eff, #63caff);
                  border-radius: 2px;
                  margin-bottom: 5px;
                  min-height: 10px;
                }

                .area-name {
                  font-size: 10px;
                  color: #a7d3ff;
                  writing-mode: vertical-rl;
                  text-orientation: mixed;
                }
              }
            }
          }
        }
      }
    }
  }

  .top-title {
    width: 100%;
    height: 80px;
    position: absolute;
    top: 0;
    z-index: 1;

    img {
      height: 80px;
    }

    .title-bg {
      width: 100%;
    }

    .title-box {
      position: absolute;
      top: 14%;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      align-items: center;
      cursor: pointer;

      .logo {
        margin-top: 4px;
        width: 48px;
        height: 48px;
      }

      .title {
        margin-left: 10px;
        font-size: 18px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        color: #ffffff;
        line-height: 52px;
        letter-spacing: 2px;
        background: linear-gradient(
          180deg,
          #3da0ff 0%,
          #e6fdff 50%,
          #68b1ff 100%
        );
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  .time {
    position: absolute;
    top: 16px;
    right: 20px;
    color: #a7d3ff;
  }

  .left {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 48px;
    height: 90%;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .bottom {
    width: 55%;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);

    img {
      width: 100%;
    }
  }

  .right {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 48px;
    height: 90%;

    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
