#!/bin/bash

# 静态大屏构建脚本

echo "开始构建静态大屏..."

# 安装依赖
echo "安装依赖..."
pnpm install

# 构建项目
echo "构建项目..."
pnpm build

# 检查构建结果
if [ -f "dist/static-screen.html" ]; then
    echo "✅ 静态大屏构建成功！"
    echo "📁 构建文件位置: dist/"
    echo "🌐 主应用访问: http://localhost/"
    echo "📺 静态大屏访问: http://localhost:8081/"
else
    echo "❌ 构建失败，请检查错误信息"
    exit 1
fi

# 构建Docker镜像（可选）
read -p "是否构建Docker镜像？(y/n): " build_docker
if [ "$build_docker" = "y" ] || [ "$build_docker" = "Y" ]; then
    echo "构建Docker镜像..."
    docker build -f Dockerfile.static-screen -t static-screen-app .
    echo "✅ Docker镜像构建完成！"
    echo "🐳 运行命令: docker run -p 80:80 -p 8081:8081 static-screen-app"
fi

echo "🎉 构建完成！"
