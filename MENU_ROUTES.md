# 智慧售电平台 - 系统菜单路由文档

## 概述

本文档包含了智慧售电平台系统中所有菜单的路由路径信息，用于系统嵌入和集成参考。

## 路由结构说明

- **Path**: 路由路径
- **Name**: 路由名称
- **Title**: 菜单标题
- **Icon**: 菜单图标
- **Rank**: 菜单排序
- **ShowLink**: 是否在菜单中显示
- **Level**: 菜单层级（0=顶级，1=二级，2=三级）

---

## 1. 首页模块 (Rank: 0)

| 路径       | 名称    | 标题   | 图标       | 层级 | 显示 |
| ---------- | ------- | ------ | ---------- | ---- | ---- |
| `/`        | Home    | 首页   | homeFilled | 0    | ✅   |
| `/welcome` | Welcome | 　首页 | homeFilled | 1    | ❌   |

## 2. 客户管理模块 (Rank: 1)

| 路径                                  | 名称               | 标题             | 图标                                        | 层级 | 显示 |
| ------------------------------------- | ------------------ | ---------------- | ------------------------------------------- | ---- | ---- |
| `/customer-management`                | CustomerManagement | 客户管理         | material-symbols:supervisor-account-rounded | 0    | ✅   |
| `/customer-record/index`              | CustomerRecord     | 　客户档案       | -                                           | 1    | ✅   |
| `/customer-management/customCreate`   | CustomerCreate     | 　新增客户       | -                                           | 1    | ❌   |
| `/customer-management/customUpdate`   | customUpdate       | 　编辑客户       | -                                           | 1    | ❌   |
| `/customer-management/customInfo`     | CustomerInfomation | 　客户信息       | -                                           | 1    | ❌   |
| `/crm-management`                     | CrmManagement      | 　 CRM 管理      | -                                           | 1    | ✅   |
| `/crm-management/sales-cockpit/index` | SalesCockpit       | 　　驾驶舱       | -                                           | 2    | ✅   |
| `/salesman/index`                     | salesman           | 　　营销人员管理 | -                                           | 2    | ✅   |
| `/userPortrait`                       | UserPortrait       | 　用户画像       | -                                           | 1    | ✅   |

## 3. 负荷管理模块 (Rank: 3)

| 路径                                       | 名称                                | 标题           | 图标            | 层级 | 显示 |
| ------------------------------------------ | ----------------------------------- | -------------- | --------------- | ---- | ---- |
| `/load-forecasting`                        | LoadForecasting                     | 负荷管理       | ep:trend-charts | 0    | ✅   |
| `/load-forecasting/analysis`               | LoadForecastingAnalysis             | 　负荷数据分析 | -               | 1    | ✅   |
| `/load-forecasting/forecast`               | LoadForecastingForecast             | 　负荷预测     | -               | 1    | ✅   |
| `/load-forecasting/load-management`        | LoadForecastingLoadManagement       | 　用能分析     | -               | 1    | ✅   |
| `/load-forecasting/load-management/detail` | LoadForecastingLoadManagementDetail | 　用能分析详情 | -               | 1    | ❌   |
| `/load-forecasting/declared-electricity`   | LoadForecastingDeclaredElectricity  | 　申报电量管理 | -               | 1    | ✅   |

## 4. 中长期交易模块 (Rank: 4)

| 路径                                    | 名称                       | 标题            | 图标                                        | 层级 | 显示 |
| --------------------------------------- | -------------------------- | --------------- | ------------------------------------------- | ---- | ---- |
| `/mAndLTerm`                            | mAndLTerm                  | 中长期交易      | material-symbols:supervisor-account-rounded | 0    | ✅   |
| `/mAndLTerm/contractAdmin`              | contractAdmin              | 　合约管理      | -                                           | 1    | ✅   |
| `/mAndLTerm/positionView`               | positionView               | 　持仓概览      | -                                           | 1    | ✅   |
| `/mAndLTerm/newEnergyD2TradingStrategy` | newEnergyD2TradingStrategy | 　 D-2 交易策略 | -                                           | 1    | ✅   |

## 5. 现货交易模块 (Rank: 5)

| 路径                            | 名称             | 标题           | 图标                                        | 层级 | 显示 |
| ------------------------------- | ---------------- | -------------- | ------------------------------------------- | ---- | ---- |
| `/inPvspotTrading`              | inPvspotTrading  | 现货交易       | material-symbols:supervisor-account-rounded | 0    | ✅   |
| `/inPvspotTrading/tradStrategy` | tradStrategy     | 　现货交易策略 | -                                           | 1    | ✅   |
| `/spotTrading/priceForecasting` | priceForecasting | 　价格预测     | -                                           | 1    | ✅   |

## 6. 结算管理模块 (Rank: 6)

| 路径                   | 名称     | 标题       | 图标                                        | 层级 | 显示 |
| ---------------------- | -------- | ---------- | ------------------------------------------- | ---- | ---- |
| `/ratail`              | ratail   | 结算管理   | material-symbols:supervisor-account-rounded | 0    | ✅   |
| `/ratail/monthSum`     | monthSum | 　用户结算 | -                                           | 1    | ✅   |
| `/ratail/setMeal`      | setMeal  | 　套餐定制 | -                                           | 1    | ✅   |
| `/ratail/setMeal/edit` | edit     | 　套餐编辑 | -                                           | 1    | ❌   |

## 7. 数据管理模块 (Rank: 8)

| 路径                           | 名称             | 标题           | 图标                                        | 层级 | 显示 |
| ------------------------------ | ---------------- | -------------- | ------------------------------------------- | ---- | ---- |
| `/dataManage`                  | dataManage       | 数据管理       | material-symbols:supervisor-account-rounded | 0    | ✅   |
| `/dataManage/peakFlatValley`   | peakFlatValley   | 　峰平谷       | -                                           | 1    | ✅   |
| `/dataManage/screenDataManage` | screenDataManage | 　大屏数据管理 | -                                           | 1    | ✅   |
| `/dataManage/marketData`       | marketData       | 　市场数据     | -                                           | 1    | ✅   |

## 8. 系统管理模块 (Rank: 9)

| 路径                                    | 名称           | 标题       | 图标                     | 层级 | 显示 |
| --------------------------------------- | -------------- | ---------- | ------------------------ | ---- | ---- |
| `/sys-management`                       | SysManagement  | 系统管理   | ant-design:tool-outlined | 0    | ✅   |
| `/sys-management/dict-management/index` | DictManagement | 　字典管理 | -                        | 1    | ✅   |
| `/sys-management/city-management/index` | CityManagement | 　地市管理 | -                        | 1    | ✅   |

## 9. 大屏展示模块

| 路径          | 名称      | 标题     | 图标 | 层级 | 显示 |
| ------------- | --------- | -------- | ---- | ---- | ---- |
| `/big-screen` | BigScreen | 大屏展示 | -    | 0    | ✅   |

## 10. 嵌入系统模块 (Rank: 10)

| 路径          | 名称      | 标题       | 图标       | 层级 | 显示 |
| ------------- | --------- | ---------- | ---------- | ---- | ---- |
| `/embed`      | Embed     | 嵌入系统   | ep:monitor | 0    | ✅   |
| `/embed/demo` | EmbedDemo | 　嵌入演示 | ep:view    | 1    | ✅   |

## 11. 特殊路由

| 路径                  | 名称     | 标题      | 图标 | 层级 | 显示 |
| --------------------- | -------- | --------- | ---- | ---- | ---- |
| `/login`              | Login    | 登录      | -    | 0    | ❌   |
| `/redirect/:path(.*)` | Redirect | 加载中... | -    | 0    | ❌   |
| `/error/403`          | 403      | 403       | -    | 0    | ❌   |
| `/error/404`          | 404      | 404       | -    | 0    | ❌   |
| `/error/500`          | 500      | 500       | -    | 0    | ❌   |

---

## 嵌入系统使用说明

### 1. iframe 嵌入方式

```html
<!-- 嵌入特定页面 -->
<iframe
  src="http://your-domain.com/customer-record/index"
  width="100%"
  height="600px"
  frameborder="0"
>
</iframe>
```

### 2. 路由参数传递

```javascript
// 通过URL参数传递数据
const url = `http://your-domain.com/customer-record/index?id=123&type=view`;
```

### 3. 登录状态说明

- iframe 嵌套的页面需要独立的登录状态管理
- 建议使用 token 方式进行身份验证
- 可以通过 URL 参数或 postMessage 传递认证信息

---

_文档生成时间: ${new Date().toLocaleString()}_
