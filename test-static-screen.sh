#!/bin/bash

echo "🚀 测试静态大屏..."

# 检查依赖
if ! command -v pnpm &> /dev/null; then
    echo "❌ pnpm 未安装，请先安装 pnpm"
    exit 1
fi

# 安装依赖
echo "📦 安装依赖..."
pnpm install

# 启动开发服务器
echo "🔧 启动开发服务器..."
pnpm dev &
DEV_PID=$!

# 等待服务器启动
echo "⏳ 等待服务器启动..."
sleep 10

# 检查服务器是否启动成功
if curl -s http://localhost:8080 > /dev/null; then
    echo "✅ 开发服务器启动成功！"
    echo "🌐 主应用访问地址: http://localhost:8080"
    echo "📺 静态大屏访问地址: http://localhost:8080/static-screen.html"
    echo ""
    echo "按 Ctrl+C 停止服务器"
    
    # 等待用户中断
    wait $DEV_PID
else
    echo "❌ 服务器启动失败"
    kill $DEV_PID 2>/dev/null
    exit 1
fi
