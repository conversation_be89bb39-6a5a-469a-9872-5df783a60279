import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { layoutConfigManager, type LayoutConfig } from '@/config/layout';

// 布局配置组合式函数
export function useLayoutConfig() {
  const config = reactive<LayoutConfig>(layoutConfigManager.getConfig());
  
  // 配置变更处理函数
  const handleConfigChange = (newConfig: LayoutConfig) => {
    Object.assign(config, newConfig);
  };

  onMounted(() => {
    layoutConfigManager.addListener(handleConfigChange);
  });

  onUnmounted(() => {
    layoutConfigManager.removeListener(handleConfigChange);
  });

  // 更新配置
  const updateConfig = (newConfig: Partial<LayoutConfig>) => {
    layoutConfigManager.updateConfig(newConfig);
  };

  // 应用预设
  const applyPreset = (presetName: string) => {
    layoutConfigManager.applyPreset(presetName);
  };

  // 切换头部显示
  const toggleHeader = () => {
    updateConfig({
      header: { ...config.header, show: !config.header.show }
    });
  };

  // 切换侧边栏显示
  const toggleSidebar = () => {
    updateConfig({
      sidebar: { ...config.sidebar, show: !config.sidebar.show }
    });
  };

  // 切换标签页显示
  const toggleTabs = () => {
    updateConfig({
      tabs: { ...config.tabs, show: !config.tabs.show }
    });
  };

  // 切换页脚显示
  const toggleFooter = () => {
    updateConfig({
      footer: { ...config.footer, show: !config.footer.show }
    });
  };

  // 进入嵌入模式
  const enterEmbedMode = (mode: 'iframe' | 'minimal' | 'simple' = 'iframe') => {
    applyPreset(mode);
  };

  // 退出嵌入模式
  const exitEmbedMode = () => {
    applyPreset('normal');
  };

  // 生成嵌入URL
  const generateEmbedUrl = (path: string, options?: any) => {
    return layoutConfigManager.generateEmbedUrl(path, options);
  };

  return {
    config,
    updateConfig,
    applyPreset,
    toggleHeader,
    toggleSidebar,
    toggleTabs,
    toggleFooter,
    enterEmbedMode,
    exitEmbedMode,
    generateEmbedUrl,
    isEmbedMode: () => layoutConfigManager.isEmbedMode(),
    isMinimalMode: () => layoutConfigManager.isMinimalMode(),
  };
}

// 布局显示控制组合式函数
export function useLayoutDisplay() {
  const { config } = useLayoutConfig();

  return {
    // 头部相关
    showHeader: () => config.header.show,
    showHeaderLogo: () => config.header.show && config.header.showLogo,
    showBreadcrumb: () => config.header.show && config.header.showBreadcrumb,
    showUserInfo: () => config.header.show && config.header.showUserInfo,
    showFullscreen: () => config.header.show && config.header.showFullscreen,
    showNotice: () => config.header.show && config.header.showNotice,
    showSettings: () => config.header.show && config.header.showSettings,

    // 侧边栏相关
    showSidebar: () => config.sidebar.show,
    showSidebarLogo: () => config.sidebar.show && config.sidebar.showLogo,
    sidebarCollapsible: () => config.sidebar.show && config.sidebar.collapsible,
    sidebarDefaultCollapsed: () => config.sidebar.defaultCollapsed,

    // 标签页相关
    showTabs: () => config.tabs.show,
    showTabsIcon: () => config.tabs.show && config.tabs.showIcon,
    showTabsClose: () => config.tabs.show && config.tabs.showClose,
    showTabsRefresh: () => config.tabs.show && config.tabs.showRefresh,

    // 页脚相关
    showFooter: () => config.footer.show,
    footerText: () => config.footer.text,

    // 嵌入模式相关
    isEmbedMode: () => config.embed.mode !== 'normal',
    isMinimalMode: () => config.embed.hideAll,
    embedMode: () => config.embed.mode,
  };
}
