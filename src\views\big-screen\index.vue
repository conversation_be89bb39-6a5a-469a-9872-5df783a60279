<template>
  <section id="fullDiv" class="big-screen-box" style="margin: 0">
    <div class="top-title">
      <img
        class="title-bg"
        src="/src/assets/images/BigScreen/title-bg.png"
        alt=""
      />
      <div class="title-box" @click="toggle">
        <img
          class="logo"
          src="/src/assets/images/BigScreen/stateGrid.png"
          alt=""
        />
        <div class="title">国网江西综合能源服务有限公司-智慧售电平台</div>
      </div>
    </div>
    <!-- 右侧时间 -->
    <div class="time">{{ dayjs(time).format("YYYY-MM-DD HH:mm:ss") }}</div>
    <div class="left">
      <img src="/src/assets/images/BigScreen/left-bg.png" alt="" />
    </div>
    <div class="bottom">
      <img src="/src/assets/images/BigScreen/bottom-bg.png" alt="" />
    </div>
    <div class="right">
      <img src="/src/assets/images/BigScreen/right-bg.png" alt="" />
    </div>
    <!-- 主题内容部分 -->
    <div class="main-content">
      <div class="edge-module">
        <div class="left-module1">
          <div class="title-box">
            <div class="title">公司简介</div>
            <div
              class="pb-[8px] pr-[10px]"
              style="position: absolute; top: -3rem; left: 21rem"
            >
              <el-select
                @change="handleYearChange"
                v-model="year"
                class="m-2"
                :teleported="false"
                placeholder="请选择"
                size="large"
                style="width: 110px"
              >
                <el-option
                  v-for="item in yearOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
          <div class="module-content">
            <vue3ScrollSeamless
              class="box"
              :dataList="list"
              :classOptions="classOptions"
            >
              <ul class="ui-wrap">
                <li class="li-item" v-for="(item, i) of list" :key="i">
                  <p style="text-indent: 2em">{{ item }}</p>
                </li>
              </ul>
            </vue3ScrollSeamless>
            <!--            <el-carousel :interval="5000" indicator-position="none" direction="vertical" height="190px" class="box">-->
            <!--              <el-carousel-item>-->
            <!--                <div style="text-indent: 2em">-->
            <!--                  江西赣能能源服务有限公司（以下简称“赣能能源”）于2019年5月23日，-->
            <!--                  由江西赣能股份有限公司、江西省天然气集团有限公司和中国电建江西省电力设计院有限公司共同出资设立。-->
            <!--                  公司致力于打造成为集发、售、配电及油、气、节能服务于一体的综合能源服务商。-->
            <!--                  从售电业务起步，逐步涉入配电，增值服务及综合能源利用领域。秉承“赣能能源，您的能源贴心管家”的服务理念，-->
            <!--                  为用户提供一站式的全方位、多元化综合能源及个性化增值服务。-->
            <!--                </div>-->
            <!--              </el-carousel-item>-->
            <!--              <el-carousel-item>-->
            <!--                <div style="text-indent: 2em">-->
            <!--                  江西赣能能源还承接了赣能股份营销中心的发电侧电力营销职能，-->
            <!--                  负责保障和落实赣能股份所属电厂市场交易电量合同。-->
            <!--                  通过对各项交易规则和政策文件的研究，加强与其他发电集团的协同与交流合作，-->
            <!--                  在省内电力市场化交易及相关规则制定过程中努力维护发电企业及公司的合理利益和市场地位。-->
            <!--                </div>-->
            <!--              </el-carousel-item>-->
            <!--            </el-carousel>-->
          </div>
        </div>
        <div class="left-module2">
          <div class="title-box">
            <div class="title">收益概览</div>
            <div class="value pb-[6px]">
              年累计：{{ totalBarValue.yearFeeSum }}
            </div>
          </div>
          <div class="mt-[30px]">
            <ChartsPictorialBar
              :legend-data="monenyLegend"
              :series="monenySeries"
              y-axis-name1="万元"
              height="250px"
            />
          </div>
        </div>
        <div class="left-module3">
          <div class="title-box">
            <div class="title">结算管理</div>
            <div class="value pb-[6px]">
              年累计：{{ totalBarValue.yearElectricitySum }}
            </div>
          </div>
          <div class="mt-[30px]">
            <ChartsPictorialBar
              :legend-data="settleLegend"
              :series="settleSeries"
              y-axis-name1="MWh"
              y-axis-name2="元/MWh"
              height="250px"
            />
          </div>
        </div>
      </div>
      <div class="center-module">
        <div class="center-card">
          <div
            class="card-wrapper"
            style="display: flex; justify-content: center"
          >
            <div class="card-box">
              <div class="card-title">签约用户</div>
              <div class="card-value">
                <!-- <div class="value">{{ homeData.signedCount }}</div> -->
                <div class="value">{{ homeData?.contractUser }}</div>
                <div class="unit">家</div>
              </div>
            </div>
            <div class="card-box">
              <div class="card-title">目标电量</div>
              <div class="card-value">
                <div class="value">
                  {{ homeData?.targetPower }}
                </div>
                <div class="unit">MWh</div>
              </div>
            </div>
            <!--            <div class="card-box">-->
            <!--              <div class="card-title">全省占比</div>-->
            <!--              <div class="card-value">-->
            <!--                <div class="value">-->
            <!--                  {{ String(homeData.provinceRatio).replace("%", "") }}-->
            <!--                </div>-->
            <!--                <div class="unit">%</div>-->
            <!--              </div>-->
            <!--            </div>-->
          </div>
          <div class="card-wrapper mt-[15px]">
            <div class="card-box">
              <div class="card-title">合同电量</div>
              <div class="card-value">
                <div class="value">
                  {{ homeData?.contractElectricity }}
                </div>
                <div class="unit">MWh</div>
              </div>
            </div>

            <div class="card-box">
              <div class="card-title">结算电量</div>
              <div class="card-value">
                <div class="value">
                  {{ homeData?.settleElectricity }}
                </div>
                <div class="unit">MWh</div>
              </div>
            </div>
            <div class="card-box">
              <div class="card-title">完成率</div>
              <div class="card-value">
                <div class="value">
                  {{
                    String(homeData?.completionRate).replace("%", "") ==
                    "undefined"
                      ? ""
                      : String(homeData?.completionRate).replace("%", "")
                  }}
                </div>
                <div class="unit">%</div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <ChartsMap
            @update="handleMapChange"
            :series="mapSeries"
            height="700px"
          />
        </div>
        <div class="center-content">
          <!-- 柱状图 -->
          <div class="chart-box">
            <ChartsBar :y-data="areayAxis" :series="areaSeries" />
          </div>
        </div>
      </div>
      <div class="edge-module">
        <div class="right-module1">
          <div class="title-box">
            <div class="title">代理大用户合同电量(MWh)</div>
          </div>
          <vue3ScrollSeamless
            class="scroll-warp content"
            :dataList="userList"
            :classOptions="classOptions"
          >
            <div v-for="(item, index) in userList" :key="index">
              <div
                class="li"
                v-for="(subItem, subIndex) in item"
                :key="subIndex"
              >
                <div class="name" :title="subItem.name">{{ subItem.name }}</div>
                <div class="value">
                  {{ subItem.value }}
                </div>
              </div>
            </div>
          </vue3ScrollSeamless>
          <!--          <el-carousel :interval="5000" indicator-position="none" direction="vertical" height="210px" class="content">-->
          <!--            <el-carousel-item v-for="(item, index) in userList" :key="index">-->
          <!--              <div class="li" v-for="(subItem, subIndex) in item" :key="subIndex">-->
          <!--                <div class="name" :title="subItem.name">{{ subItem.name }}</div>-->
          <!--                <div class="value">-->
          <!--                  {{ subItem.value }}-->
          <!--                </div>-->
          <!--              </div>-->
          <!--            </el-carousel-item>-->
          <!--          </el-carousel>-->
        </div>
        <div class="right-module2">
          <div class="title-box">
            <div class="title">用户画像分析</div>
            <div class="pb-[8px] pr-[10px]">
              <!-- <el-select
                style="width: 220px; margin-left: 15px"
                v-model="userPortrait"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select> -->
              <!--              <DictSelect style="width: 90px" @change="handleSelectChange" v-model="userPortrait" size="small"-->
              <!--                :teleported="false" :clearable="false" dict-code="userPortrait" />-->
              <el-select
                style="width: 90px"
                @change="handleSelectChange"
                v-model="userPortrait"
                size="small"
                :teleported="false"
                :clearable="false"
              >
                <el-option :value="1" label="电量等级"></el-option>
              </el-select>
            </div>
          </div>
          <div class="mt-[30px]">
            <ChartsPie
              @update="handlePieChange"
              class="chartsPie-box"
              height="220px"
              :series="pieSeries"
            />
          </div>
        </div>
        <div class="right-module3">
          <div class="title-box">
            <div class="title">销售漏斗</div>
            <div></div>
          </div>
          <div class="funnel-box">
            <img
              src="/src/assets/images/BigScreen/funel.png"
              class="h-[170px] w-[320px]"
              alt=""
            />
            <div class="legend-box">
              <div
                class="content"
                v-for="(item, index) in funnelSeries"
                :key="index"
                @click="handleChange(item.id)"
              >
                <div class="label">{{ item.name }}</div>
                <div class="value">({{ item.value }})</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog v-model="dialogVisible" :title="areaName" width="60%">
      <list :area-id="areaId" />
    </el-dialog>
    <el-dialog v-model="dialogVisible1" title="数据详情" width="60%">
      <pie-list :query-id="queryId" :type="userPortrait" :tags="tags" />
    </el-dialog>
    <el-dialog v-model="dialogVisible2" title="数据详情" width="70%">
      <opportunity-list :query-type="queryType" />
    </el-dialog>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, onActivated } from "vue";
import dayjs, { Dayjs } from "dayjs";
import ChartsPie from "./components/charts-pie.vue";
import List from "./components/list.vue";
import PieList from "./components/pieList.vue";
import ChartsBar from "./components/charts-bar.vue";
import Funnel from "./components/Funnel.vue";
import ChartsMap from "./components/charts-map.vue";
import ChartsPictorialBar from "./components/charts-pictorialBar.vue";
import screenFull from "screenfull";
import { useIntervalFn } from "@vueuse/core";
import { max, cloneDeep } from "lodash-es";
import opportunityList from "./components/opportunityList.vue";
import { vue3ScrollSeamless } from "vue3-scroll-seamless";

import {
  getCustomerSumApi,
  getGradeCountApi,
  getTotalTop4Api,
  getAreaSumApi,
  getHomeYearTotalApi,
  getScreenSettlementApi,
  getTagGroupApi
} from "@/api/customer-management/index";
import type { EChartsOption } from "echarts";
import OpportunityList from "@/views/crm-management/sales-cockpit/components/opportunityList.vue";
import { queryScreenPageListAPI } from "@/api/bigScreenDataManage";
// import { userPortraitOptions } from "@/hooks/dict/useDictOptions";
defineOptions({
  name: "BigScreen"
});

onActivated(() => {
  getHomeYearTotal(year.value);
  queryScreenPageListAPI({
    dataYear: dayjs().format("YYYY")
  }).then(res => {
    // 分割字符串
    const parts = res[0]?.companyProfile.split("\n");
    // 处理每个部分
    const result = parts?.map(part => {
      // 去除前后空格
      return part.trim();
    });
    list.value = result;
    // console.log(homeData.value)
  });
});

const dialogVisible = ref<boolean>(false);
const dialogVisible1 = ref<boolean>(false);
const dialogVisible2 = ref<boolean>(false);
const queryType = ref("");
const queryId = ref("");
const areaId = ref("");
const tags = ref([]);
const mapSeries = ref([]);
const time = ref<Dayjs>(dayjs());
const year = ref(dayjs().format("YYYY"));
const areaName = ref<string>("");

const classOptions = {
  step: 0.1,
  limitMoveNum: 1
};

const list: any = ref([
  "江西赣能能源服务有限公司（以下简称“赣能能源”）于2019年5月23日，由江西赣能股份有限公司、江西省天然气集团有限公司和中国电建江西省电力设计院有限公司共同出资设立。",
  "公司致力于打造成为集发、售、配电及油、气、节能服务于一体的综合能源服务商。从售电业务起步，逐步涉入配电，增值服务及综合能源利用领域。秉承“赣能能源，您的能源贴心管家”的服务理念，为用户提供一站式的全方位、多元化综合能源及个性化增值服务。",
  "江西赣能能源还承接了赣能股份营销中心的发电侧电力营销职能，负责保障和落实赣能股份所属电厂市场交易电量合同。",
  "通过对各项交易规则和政策文件的研究，加强与其他发电集团的协同与交流合作，在省内电力市场化交易及相关规则制定过程中努力维护发电企业及公司的合理利益和市场地位。"
]);

// 柱状图累计值
const totalBarValue = ref({
  yearElectricitySum: 0,
  yearFeeSum: 0
});
const yearOptions = ref(
  new Array(5)
    .fill(val => val)
    .map((item, index) => {
      return {
        label: dayjs().year() + (index - 2) + "年度",
        value: dayjs().year() + (index - 2)
      };
    })
);
// 地区统计用户y轴
const areayAxis = ref<string[]>([]);
const areaSeries = ref<number[]>([]);
// 顶部数据
const homeData = ref({
  sumElectricityQty: 0,
  signedCount: 0,
  provinceRatio: 0,
  targetElectricityQty: 0,
  completeElectricityQty: 0,
  completeRatio: 0,
  settleElectricity: "",
  contractElectricity: "",
  targetPower: "",
  contractUser: "",
  companyProfile: "",
  completionRate: ""
});
const options = ref([]);
// 用户画像类型
const userPortrait = ref(1);
// 代理大用户列表
const userList = ref([]);
// 收益概览
const monenySeries = ref([
  {
    data: [200, 85, 112, 275, 305, 415, 741, 405, 200, 204, 110, 140],
    type: "bar",
    barWidth: 15,
    itemStyle: {
      color: {
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        type: "linear",
        global: false,
        colorStops: [
          {
            offset: 0,
            color: "#0b9eff"
          },
          {
            offset: 1,
            color: "#63caff"
          }
        ]
      }
    },
    label: {
      show: false
    }
  },
  {
    data: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
    type: "pictorialBar",
    symbol: "diamond",
    symbolOffset: [0, "50%"],
    tooltip: {
      show: false
    },
    symbolSize: [15, 15]
  },
  {
    name: "收益",
    data: [200, 85, 112, 275, 305, 415, 741, 405, 200, 204, 110, 140],
    type: "pictorialBar",
    symbolPosition: "end",
    symbol: "diamond",
    symbolOffset: [0, "-50%"],
    symbolSize: [15, 12],
    zlevel: 2
  },
  {
    data: [741, 741, 741, 741, 741, 741, 741, 741, 741, 741, 741, 741],
    type: "bar",
    barWidth: 15,
    barGap: "-100%",
    itemStyle: {
      color: "#1462BD",
      opacity: 0.2
    },
    tooltip: {
      show: false
    },
    zlevel: -1
  },
  {
    data: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
    type: "pictorialBar",
    symbol: "diamond",
    symbolOffset: [0, "50%"],
    tooltip: {
      show: false
    },
    symbolSize: [15, 15],
    zlevel: -2
  },
  {
    data: [741, 741, 741, 741, 741, 741, 741, 741, 741, 741, 741, 741],
    type: "pictorialBar",
    symbolPosition: "end",
    symbol: "diamond",
    itemStyle: {
      color: "#1462BD",
      opacity: 0.2
    },
    tooltip: {
      show: false
    },
    symbolOffset: [0, "-50%"],
    symbolSize: [15, 12],
    zlevel: -1
  }
]);
const monenyLegend: EChartsOption["legend"] = [
  {
    name: "收益",
    textStyle: {
      color: "#EAF1FF"
    },
    itemStyle: {
      color: "#2762E3"
    },
    icon: "roundRect"
  }
];
// 结算管理
const settleSeries = ref([
  {
    yAxisIndex: 0,
    name: "结算电量",
    data: [200, 85, 112, 275, 305, 415, 741, 405, 200, 204, 110, 140],
    type: "bar",
    barWidth: 15,
    itemStyle: {
      color: {
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        type: "linear",
        global: false,
        colorStops: [
          {
            offset: 0,
            color: "#0b9eff"
          },
          {
            offset: 1,
            color: "#63caff"
          }
        ]
      }
    },
    label: {
      show: false
    }
  },
  {
    data: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
    type: "pictorialBar",
    symbol: "diamond",
    symbolOffset: [0, "50%"],
    tooltip: {
      show: false
    },
    symbolSize: [15, 15]
  },
  {
    name: "结算电量",
    yAxisIndex: 0,
    data: [200, 85, 112, 275, 305, 415, 741, 405, 200, 204, 110, 140],
    type: "pictorialBar",
    symbolPosition: "end",
    symbol: "diamond",
    symbolOffset: [0, "-50%"],
    symbolSize: [15, 12],
    zlevel: 2
  },
  {
    yAxisIndex: 0,
    data: [741, 741, 741, 741, 741, 741, 741, 741, 741, 741, 741, 741],
    type: "bar",
    barWidth: 15,
    tooltip: {
      show: false
    },
    barGap: "-100%",
    itemStyle: {
      color: "#1462BD",
      opacity: 0.2
    },
    zlevel: -1
  },
  {
    data: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
    type: "pictorialBar",
    symbol: "diamond",
    tooltip: {
      show: false
    },
    symbolOffset: [0, "50%"],
    symbolSize: [15, 15],
    zlevel: -2
  },
  {
    yAxisIndex: 0,
    data: [741, 741, 741, 741, 741, 741, 741, 741, 741, 741, 741, 741],
    type: "pictorialBar",
    symbolPosition: "end",
    tooltip: {
      show: false
    },
    symbol: "diamond",
    itemStyle: {
      color: "#1462BD",
      opacity: 0.2
    },
    symbolOffset: [0, "-50%"],
    symbolSize: [15, 12],
    zlevel: -1
  },
  {
    name: "结算电价",
    yAxisIndex: 1,
    data: [180, 65, 92, 255, 295, 385, 701, 355],
    type: "line",
    itemStyle: {
      color: "#5EF4FF"
    },
    // symbolPosition: "end",
    // symbol: "diamond",
    // symbolOffset: [0, "-50%"],
    // symbolSize: [30, 12],
    zlevel: 2
  }
]);
const settleLegend: EChartsOption["legend"] = [
  {
    name: "结算电量",
    textStyle: {
      color: "#EAF1FF"
    },
    itemStyle: {
      color: "#2762E3"
    },
    icon: "roundRect"
  },
  {
    name: "结算电价",
    textStyle: {
      color: "#EAF1FF"
    },
    itemStyle: {
      color: "#2762E3"
    }
  }
];
// 用户画像
const pieSeries = ref([
  {
    name: "用户画像分析",
    type: "pie",
    radius: ["40%", "70%"],
    // center: ["50%", "50%"],
    label: {
      show: true,
      color: "#fff",
      formatter: "{b}  {c}",
      position: "outside"
    },
    minAngle: 20, //最小的扇区角度（0 ~ 360），用于防止某个值过小导致扇区太小影响交互
    avoidLabelOverlap: true,
    labelLine: {
      show: true
    },
    itemStyle: {
      borderColor: "#47A7C8",
      borderWidth: 1
    },
    data: [
      { value: 100, name: "A类" },
      { value: 140, name: "B类" },
      { value: 210, name: "C类" },
      { value: 50, name: "D类" }
    ]
  }
]);
const funnelSeries = ref([]);
async function getTagGroup(type) {
  const res = await getTagGroupApi({
    tagType: type
  });
  if (res.data) {
    pieSeries.value[0].data = res.data.map(item => {
      return {
        value: item.labelCount,
        name: item.label,
        id: undefined,
        tagValueId: item.tagValueId,
        tagType: item.tagType
      };
    });
  }
  console.log(res);
}
async function getList() {
  const res = await getTotalTop4Api();
  if (res.data) {
    funnelSeries.value = res.data.map(item => {
      return {
        name: item.name,
        value: item.count,
        id: item.id
      };
    });
  }
}
function handleYearChange(year) {
  getHomeYearTotal(year);
  getScreenSettlement();
}
function filterValue(value) {
  return ["-", null, undefined].includes(value)
    ? "-"
    : (value ? Number((value * 10).toFixed(2)) : 0)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}
function toggle() {
  const element = document.getElementById("fullDiv");
  screenFull.toggle(element);
}
function setTime() {
  time.value = dayjs();
}
async function getCustomerSum() {
  const res = await getCustomerSumApi();
  if (res.data) {
    const arr = res.data.map(item => {
      return {
        name: item.customName,
        value: item.sumElectricityQty
      };
    });
    userList.value = splitArr(arr, 4);
  }
}
async function getAreaSum() {
  const res = await getAreaSumApi();
  if (res.data) {
    const array = cloneDeep(res.data);
    // array.reverse();
    areayAxis.value = res.data.map(i => i.areaName).reverse();
    areaSeries.value = res.data.map(i => i.sumElectricityQty).reverse();
    array.forEach((item, index) => {
      mapSeries.value.push({
        name: item.areaName,
        value: index + 1
      });
    });
  }
}
// 用户画像饼图
async function getGradeCount() {
  const res = await getGradeCountApi();
  if (res.data) {
    pieSeries.value[0].data = res.data.map(item => {
      return {
        value: item.customCount,
        name: item.customGradeName,
        id: item.customGradeId
      };
    });
  }
}
// 顶部数据
async function getHomeYearTotal(year) {
  await queryScreenPageListAPI({
    dataYear: year
  }).then(res => {
    homeData.value = res[0];
    // console.log(homeData.value)
  });
  // const res = await getHomeYearTotalApi(year);
  // if (res.data) {
  //   homeData.value = { ...res.data };
  // }
}
// 柱状图数据
async function getScreenSettlement() {
  const res = await getScreenSettlementApi({
    parameter: {
      year: year.value
    }
  });
  if (res.data?.curveList) {
    const monenyTotal = Number(
      max(res.data.curveList.map(i => (i.fee !== null ? i.fee : 0)))
    ).toFixed(0);
    const settleTotal = Number(
      max(
        res.data.curveList.map(i =>
          i.electricity !== null ? i.electricity : 0
        )
      )
    ).toFixed(0);
    monenySeries.value[3].data = new Array(12)
      .fill(item => item)
      .map(() => Number(monenyTotal));
    monenySeries.value[5].data = monenySeries.value[3].data;
    monenySeries.value[0].data = res.data.curveList.map(i => i.fee);
    monenySeries.value[2].data = monenySeries.value[0].data;

    settleSeries.value[3].data = new Array(12)
      .fill(item => item)
      .map(() => Number(settleTotal));
    settleSeries.value[5].data = settleSeries.value[3].data;
    settleSeries.value[0].data = res.data.curveList.map(i => i.electricity);
    settleSeries.value[2].data = settleSeries.value[0].data;
    settleSeries.value[6].data = res.data.curveList.map(i => i.price);
    totalBarValue.value.yearElectricitySum = res.data.yearElectricitySum;
    totalBarValue.value.yearFeeSum = res.data.yearFeeSum;
  } else {
    monenySeries.value[3].data = [];
    monenySeries.value[5].data = monenySeries.value[3].data;
    monenySeries.value[0].data = [];
    monenySeries.value[2].data = monenySeries.value[0].data;

    settleSeries.value[3].data = [];
    settleSeries.value[5].data = settleSeries.value[3].data;
    settleSeries.value[0].data = [];
    settleSeries.value[2].data = settleSeries.value[0].data;
    settleSeries.value[6].data = [];
    totalBarValue.value.yearElectricitySum = 0;
    totalBarValue.value.yearFeeSum = 0;
  }
}
async function handleSelectChange(data) {
  if (data.value === 1) {
    getGradeCount();
    return;
  }
  const map = {
    2: "electricityConsumptionCharacteristics",
    3: "deviationCharacteristics"
  };
  getTagGroup(map[data.value]);
}
async function handleChange(id) {
  dialogVisible2.value = true;
  queryType.value = id;
}
function handlePieChange(obj) {
  if (obj) {
    dialogVisible1.value = true;
    if (userPortrait.value !== 1) {
      tags.value = [obj];
    }
    queryId.value = obj.id;
  }
}
function handleMapChange(obj) {
  if (obj) {
    dialogVisible.value = true;
    areaId.value = obj.id;
    areaName.value = obj.areaName + "客户列表";
  }
}
/**
 * 分割数组创建二维数组封装
 * @param data 数组
 * @param senArrLen 需要分割成子数组的长度
 */
function splitArr(data, senArrLen) {
  //处理成len个一组的数据
  let data_len = data.length;
  let arrOuter_len =
    data_len % senArrLen === 0
      ? data_len / senArrLen
      : parseInt(data_len / senArrLen + "") + 1;
  let arrSec_len = data_len > senArrLen ? senArrLen : data_len; //内层数组的长度
  let arrOuter = new Array(arrOuter_len); //最外层数组
  let arrOuter_index = 0; //外层数组的子元素下标
  // console.log(data_len % len);
  for (let i = 0; i < data_len; i++) {
    if (i % senArrLen === 0) {
      arrOuter_index++;
      let len = arrSec_len * arrOuter_index;
      //将内层数组的长度最小取决于数据长度对len取余，平时最内层由下面赋值决定
      arrOuter[arrOuter_index - 1] = new Array(data_len % senArrLen);
      if (arrOuter_index === arrOuter_len)
        //最后一组
        data_len % senArrLen === 0
          ? (len = (data_len % senArrLen) + senArrLen * arrOuter_index)
          : (len = (data_len % senArrLen) + senArrLen * (arrOuter_index - 1));
      let arrSec_index = 0; //第二层数组的索引
      for (let k = i; k < len; k++) {
        //第一层数组的开始取决于第二层数组长度*当前第一层的索引
        arrOuter[arrOuter_index - 1][arrSec_index] = data[k];
        arrSec_index++;
      }
    }
  }
  return arrOuter;
}
const { pause, resume } = useIntervalFn(setTime, 1000);
onMounted(() => {
  resume();
  getList();
  getCustomerSum();
  getAreaSum();
  getGradeCount();
  getHomeYearTotal(year.value);
  getScreenSettlement();
  queryScreenPageListAPI({
    dataYear: "2024"
  }).then(res => {
    // 分割字符串
    const parts = res[0]?.companyProfile?.split("\n");
    // 处理每个部分
    const result = parts?.map(part => {
      // 去除前后空格
      return part.trim();
    });
    list.value = result;
    // console.log(homeData.value)
  });
});
onUnmounted(() => {
  pause();
});
</script>

<style lang="scss" scoped>
.big-screen-box {
  :deep .el-dialog {
    background-color: rgba(1, 34, 59, 0.9);
  }

  :deep .el-dialog__header {
    border-bottom: 1px solid #3f6588;
  }

  :deep .el-dialog__title {
    color: #fff;
  }

  :deep .el-table th.el-table__cell {
    background: rgba(1, 34, 59, 1) !important;
  }

  :deep .el-table tr {
    background: rgba(1, 34, 59, 1) !important;
  }

  :deep .el-table .cell {
    color: #fff !important;
  }

  :deep .el-table th.el-table__cell.is-leaf,
  :deep .el-table td.el-table__cell,
  :deep .el-table__inner-wrapper::before {
    border-bottom: 1px solid #3f6588;
  }

  :deep
    .el-table--enable-row-hover
    .el-table__body
    tr:hover
    > td.el-table__cell {
    background: transparent;
  }

  :deep .el-scrollbar__wrap,
  :deep .el-pagination.is-background .el-pager li {
    background: none !important;
    color: #fff;
  }

  :deep .el-pagination.is-background .btn-prev:disabled,
  :deep .el-pagination.is-background .btn-next:disabled {
    color: #fff;
    background: none;
  }

  :deep .el-table__empty-block {
    background: rgba(1, 34, 59, 1) !important;
  }

  :deep .el-input__wrapper {
    background: transparent;
    box-shadow: 0 0 0 1px transparent !important;
  }

  :deep .el-input__inner,
  :deep .el-pagination__total {
    color: #fff;
  }

  width: 100%;
  min-height: 1080px;
  height: auto;
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  position: relative;
  // padding-top: 20px;
  background: url("/src/assets/images/BigScreen/bg.png") no-repeat;
  background-size: 100% 100%;
  background-attachment: fixed;

  // 全屏状态下的适配
  &:fullscreen {
    height: 100vh;
    min-height: 100vh;
    overflow: hidden;

    .main-content {
      height: calc(100vh - 100px);
      padding: 100px 45px 20px 45px;
    }
  }

  // 非全屏状态下的适配
  @media screen and (max-height: 1080px) {
    min-height: 100vh;
    height: auto;
    overflow-y: auto;

    .main-content {
      padding: 80px 45px 20px 45px;
    }
  }

  // 小屏幕适配
  @media screen and (max-width: 1920px) {
    .main-content {
      .edge-module {
        width: 420px;

        & > div {
          height: 280px;
        }
      }

      .center-module {
        width: 580px;
      }
    }
  }

  .main-content {
    width: 100%;
    height: 100%;
    padding: 100px 45px 20px 45px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .edge-module {
      width: 480px;
      flex-shrink: 0;

      & > div {
        background: url("/src/assets/images/BigScreen/module-bg.png");
        background-size: 100% 100%;
        backdrop-filter: blur(5px);
        width: 100%;
        height: 304px;
        margin-top: 15px;

        .title-box {
          display: flex;
          height: 38px;
          align-items: center;
          justify-content: space-between;
          padding-top: 14px;
          padding-left: 38px;

          .title {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            text-shadow: 0px 0px 12px rgba(193, 227, 255, 0.93);
          }

          .value {
            padding-right: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 800;
            font-size: 14px;
            color: #ffffff;
            text-shadow: 0px 0px 12px rgba(193, 227, 255, 0.53);
          }
        }

        &:first-child {
          margin-top: 0;
        }
      }

      .left-module1 {
        :deep .el-input__wrapper {
          background: linear-gradient(
            180deg,
            rgba(88, 166, 255, 0.15) 0%,
            rgba(81, 153, 255, 0.23) 100%
          );
          border: none;
          box-shadow: none;
          color: #fff;
        }

        :deep .el-input__inner {
          color: #6e93cf;
        }

        .module-content {
          margin: 28px 25px 0px 25px;
          height: 223px;
          box-sizing: border-box;
          padding: 5px;
          background: rgba(108, 184, 255, 0.08);
          border: 0px solid rgba(110, 193, 247, 0.32);
          overflow: hidden;

          .box {
            height: 210px;
            padding: 8px;
            overflow: hidden;
            background: linear-gradient(
              180deg,
              rgba(88, 209, 255, 0.13) 0%,
              rgba(81, 192, 255, 0.16) 100%
            );
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #a7d3ff;
          }
          // 滚动样式
          .scroll-wrap {
            height: 210px;
            width: 360px;
            //margin: 0 auto;
            overflow: hidden;
          }
        }
      }

      // .left-module2 {
      //   height: 210px;
      // }
      .right-module1 {
        .content {
          margin: 20px;
          height: 223px;
          box-sizing: border-box;
          padding: 5px;
          overflow-y: hidden;

          .li {
            cursor: pointer;
            // width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(108, 184, 255, 0.08);
            border: 0px solid rgba(110, 193, 247, 0.32);
            padding: 5px 8px;
            margin-top: 10px;

            .name {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #85b7ff;
              width: 70%;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              padding: 5px;
              background: linear-gradient(
                180deg,
                rgba(88, 169, 255, 0.13) 0%,
                rgba(81, 159, 255, 0.16) 100%
              );
            }

            .value {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #ffffff;
            }
          }
        }
      }

      .right-module2 {
        :deep .el-input__wrapper {
          background: linear-gradient(
            180deg,
            rgba(88, 166, 255, 0.15) 0%,
            rgba(81, 153, 255, 0.23) 100%
          );
          border: none;
          box-shadow: none;
          color: #fff;
        }

        :deep .el-input__inner {
          color: #6e93cf;
        }

        .chartsPie-box {
          background: url("/src/assets/images/BigScreen/pie-bg.png") no-repeat;
          background-size: 70px 70px;
          background-position: center center;
        }
      }

      .right-module3 {
        .funnel-box {
          position: relative;
          display: flex;
          // align-items: center;
          // justify-content: center;
          margin-left: 40px;
          margin-top: 50px;

          .legend-box {
            position: absolute;
            top: -5px;
            right: 40px;
            color: #fff;
            font-size: 14px;

            .content {
              display: flex;
              flex-direction: column;
              align-items: center;
              margin-top: 10px;
              cursor: pointer;

              &:first-child {
                margin-top: 0;
              }

              .value {
                color: #59d7ff;
                font-size: 12px;
              }
            }
          }
        }
      }
    }

    .center-module {
      width: 655px;
      flex-shrink: 0;
      padding: 0 15px;

      .center-card {
        .card-wrapper {
          display: flex;
          //justify-content: space-between;
          justify-content: space-around;

          .card-box {
            margin-right: 15px;
            width: 33.33%;
            height: 102px;
            background: url("/src/assets/images/BigScreen/card-bg.png")
              no-repeat;
            background-size: 100% 100%;
            backdrop-filter: blur(5px);
            color: #ffffff;

            .card-title {
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;

              text-shadow: 0px 0px 12px rgba(193, 227, 255, 0.93);
              text-align: center;
              padding-top: 4px;
            }

            .card-value {
              display: flex;
              justify-content: center;
              align-items: baseline;
              line-height: 74px;

              .value {
                font-size: 31px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                color: #5bddff;
              }
            }

            &:last-child {
              margin-right: 0;
            }
          }
        }
      }

      .center-content {
        position: relative;

        .chart-box {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 290px;
          height: 300px;
        }
      }
    }
  }

  .top-title {
    width: 100%;
    height: 80px;
    position: absolute;
    top: 0;
    z-index: 1;

    img {
      height: 80px;
    }

    .title-bg {
      width: 100%;
    }

    .title-box {
      position: absolute;
      top: 14%;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      align-items: center;
      cursor: pointer;

      .logo {
        margin-top: 4px;
        width: 48px;
        height: 48px;
      }

      .title {
        margin-left: 10px;
        font-size: 18px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        color: #ffffff;
        line-height: 52px;
        letter-spacing: 2px;
        background: linear-gradient(
          180deg,
          #3da0ff 0%,
          #e6fdff 50%,
          #68b1ff 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  .time {
    position: absolute;
    top: 16px;
    right: 20px;
    color: #a7d3ff;
  }

  .left {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 48px;
    height: 90%;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .bottom {
    width: 55%;
    // height: 84px;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);

    img {
      width: 100%;
    }
  }

  .right {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 48px;
    height: 90%;

    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
