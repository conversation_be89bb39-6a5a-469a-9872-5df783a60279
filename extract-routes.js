const fs = require('fs');
const path = require('path');

// 读取路由模块目录
const routesDir = path.join(__dirname, 'src/router/modules');
const outputFile = path.join(__dirname, 'MENU_ROUTES.md');

// 存储所有路由信息
let allRoutes = [];

// 递归提取路由信息
function extractRoutes(routeObj, parentPath = '', level = 0) {
  const routes = [];
  
  if (routeObj.path && routeObj.meta?.title) {
    const fullPath = routeObj.path.startsWith('/') ? routeObj.path : `${parentPath}/${routeObj.path}`;
    routes.push({
      path: fullPath,
      name: routeObj.name || '',
      title: routeObj.meta.title,
      icon: routeObj.meta.icon || '',
      rank: routeObj.meta.rank || 999,
      showLink: routeObj.meta.showLink !== false,
      level: level,
      component: routeObj.component ? routeObj.component.toString() : '',
      redirect: routeObj.redirect || ''
    });
  }
  
  if (routeObj.children && Array.isArray(routeObj.children)) {
    routeObj.children.forEach(child => {
      const childRoutes = extractRoutes(child, routeObj.path || parentPath, level + 1);
      routes.push(...childRoutes);
    });
  }
  
  return routes;
}

// 读取所有路由模块文件
function readRouteModules() {
  const files = fs.readdirSync(routesDir).filter(file => 
    file.endsWith('.ts') && file !== 'remaining.ts'
  );
  
  files.forEach(file => {
    const filePath = path.join(routesDir, file);
    const content = fs.readFileSync(filePath, 'utf-8');
    
    // 简单的解析（实际项目中可能需要更复杂的解析）
    console.log(`Processing ${file}...`);
    
    // 这里需要手动添加每个模块的路由信息
    // 由于无法直接执行TypeScript，我们手动提取
  });
}

// 手动定义所有路由信息（基于代码分析）
const routeModules = [
  {
    name: 'home',
    routes: [
      {
        path: '/',
        name: 'Home',
        title: '首页',
        icon: 'homeFilled',
        rank: 0,
        showLink: true,
        level: 0,
        redirect: '/welcome'
      },
      {
        path: '/welcome',
        name: 'Welcome',
        title: '首页',
        icon: 'homeFilled',
        rank: 0,
        showLink: false,
        level: 1
      }
    ]
  },
  {
    name: 'customerManagement',
    routes: [
      {
        path: '/customer-management',
        name: 'CustomerManagement',
        title: '客户管理',
        icon: 'material-symbols:supervisor-account-rounded',
        rank: 1,
        showLink: true,
        level: 0
      },
      {
        path: '/customer-record/index',
        name: 'CustomerRecord',
        title: '客户档案',
        icon: '',
        rank: 1,
        showLink: true,
        level: 1
      },
      {
        path: '/customer-management/customCreate',
        name: 'CustomerCreate',
        title: '新增客户',
        icon: '',
        rank: 1,
        showLink: false,
        level: 1
      },
      {
        path: '/customer-management/customUpdate',
        name: 'customUpdate',
        title: '编辑客户',
        icon: '',
        rank: 1,
        showLink: false,
        level: 1
      },
      {
        path: '/customer-management/customInfo',
        name: 'CustomerInfomation',
        title: '客户信息',
        icon: '',
        rank: 1,
        showLink: false,
        level: 1
      },
      {
        path: '/crm-management',
        name: 'CrmManagement',
        title: 'CRM管理',
        icon: '',
        rank: 2,
        showLink: true,
        level: 1
      },
      {
        path: '/crm-management/sales-cockpit/index',
        name: 'SalesCockpit',
        title: '驾驶舱',
        icon: '',
        rank: 2,
        showLink: true,
        level: 2
      },
      {
        path: '/salesman/index',
        name: 'salesman',
        title: '营销人员管理',
        icon: '',
        rank: 2,
        showLink: true,
        level: 2
      },
      {
        path: '/userPortrait',
        name: 'UserPortrait',
        title: '用户画像',
        icon: '',
        rank: 2,
        showLink: true,
        level: 1
      }
    ]
  },
  {
    name: 'loadForecasting',
    routes: [
      {
        path: '/load-forecasting',
        name: 'LoadForecasting',
        title: '负荷管理',
        icon: 'ep:trend-charts',
        rank: 3,
        showLink: true,
        level: 0
      },
      {
        path: '/load-forecasting/analysis',
        name: 'LoadForecastingAnalysis',
        title: '负荷数据分析',
        icon: '',
        rank: 3,
        showLink: true,
        level: 1
      },
      {
        path: '/load-forecasting/forecast',
        name: 'LoadForecastingForecast',
        title: '负荷预测',
        icon: '',
        rank: 3,
        showLink: true,
        level: 1
      },
      {
        path: '/load-forecasting/load-management',
        name: 'LoadForecastingLoadManagement',
        title: '用能分析',
        icon: '',
        rank: 3,
        showLink: true,
        level: 1
      },
      {
        path: '/load-forecasting/load-management/detail',
        name: 'LoadForecastingLoadManagementDetail',
        title: '用能分析详情',
        icon: '',
        rank: 3,
        showLink: false,
        level: 1
      },
      {
        path: '/load-forecasting/declared-electricity',
        name: 'LoadForecastingDeclaredElectricity',
        title: '申报电量管理',
        icon: '',
        rank: 3,
        showLink: true,
        level: 1
      }
    ]
  }
];

// 生成Markdown文档
function generateMarkdown() {
  let markdown = `# 系统菜单路由文档

## 概述
本文档包含了智慧售电平台系统中所有菜单的路由路径信息，用于系统嵌入和集成参考。

## 路由结构说明
- **Path**: 路由路径
- **Name**: 路由名称
- **Title**: 菜单标题
- **Icon**: 菜单图标
- **Rank**: 菜单排序
- **ShowLink**: 是否在菜单中显示
- **Level**: 菜单层级（0=顶级，1=二级，2=三级）

---

`;

  // 按rank排序
  const sortedModules = routeModules.sort((a, b) => {
    const aMinRank = Math.min(...a.routes.map(r => r.rank));
    const bMinRank = Math.min(...b.routes.map(r => r.rank));
    return aMinRank - bMinRank;
  });

  sortedModules.forEach(module => {
    markdown += `## ${module.name}\n\n`;
    
    // 按level和rank排序
    const sortedRoutes = module.routes.sort((a, b) => {
      if (a.level !== b.level) return a.level - b.level;
      return a.rank - b.rank;
    });
    
    markdown += `| 路径 | 名称 | 标题 | 图标 | 层级 | 显示 |\n`;
    markdown += `|------|------|------|------|------|------|\n`;
    
    sortedRoutes.forEach(route => {
      const indent = '　'.repeat(route.level);
      markdown += `| \`${route.path}\` | ${route.name} | ${indent}${route.title} | ${route.icon} | ${route.level} | ${route.showLink ? '✅' : '❌'} |\n`;
    });
    
    markdown += `\n`;
  });

  return markdown;
}

// 写入文件
const markdownContent = generateMarkdown();
fs.writeFileSync(outputFile, markdownContent, 'utf-8');
console.log(`菜单路由文档已生成: ${outputFile}`);
