<template>
  <div class="embed-demo">
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <span>嵌入模式演示</span>
          <el-button type="primary" @click="showEmbedDialog = true">
            生成嵌入代码
          </el-button>
        </div>
      </template>

      <div class="demo-content">
        <h3>当前布局配置</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="头部显示">
            <el-tag :type="layoutDisplay.showHeader() ? 'success' : 'danger'">
              {{ layoutDisplay.showHeader() ? '显示' : '隐藏' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="侧边栏显示">
            <el-tag :type="layoutDisplay.showSidebar() ? 'success' : 'danger'">
              {{ layoutDisplay.showSidebar() ? '显示' : '隐藏' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="标签页显示">
            <el-tag :type="layoutDisplay.showTabs() ? 'success' : 'danger'">
              {{ layoutDisplay.showTabs() ? '显示' : '隐藏' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="嵌入模式">
            <el-tag :type="layoutDisplay.isEmbedMode() ? 'warning' : 'info'">
              {{ layoutDisplay.embedMode() }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <div class="demo-controls">
          <h3>布局控制</h3>
          <el-space wrap>
            <el-button @click="toggleHeader">
              {{ layoutDisplay.showHeader() ? '隐藏' : '显示' }}头部
            </el-button>
            <el-button @click="toggleSidebar">
              {{ layoutDisplay.showSidebar() ? '隐藏' : '显示' }}侧边栏
            </el-button>
            <el-button @click="toggleTabs">
              {{ layoutDisplay.showTabs() ? '隐藏' : '显示' }}标签页
            </el-button>
          </el-space>

          <h3>预设模式</h3>
          <el-space wrap>
            <el-button @click="applyPreset('normal')">正常模式</el-button>
            <el-button @click="applyPreset('iframe')">iframe模式</el-button>
            <el-button @click="applyPreset('minimal')">最小化模式</el-button>
            <el-button @click="applyPreset('simple')">简化模式</el-button>
          </el-space>
        </div>

        <div class="demo-urls">
          <h3>嵌入URL示例</h3>
          <el-space direction="vertical" style="width: 100%">
            <div>
              <strong>iframe模式:</strong>
              <el-input
                v-model="iframeUrl"
                readonly
                style="margin-top: 8px"
              >
                <template #append>
                  <el-button @click="copyUrl(iframeUrl)">复制</el-button>
                </template>
              </el-input>
            </div>
            <div>
              <strong>最小化模式:</strong>
              <el-input
                v-model="minimalUrl"
                readonly
                style="margin-top: 8px"
              >
                <template #append>
                  <el-button @click="copyUrl(minimalUrl)">复制</el-button>
                </template>
              </el-input>
            </div>
          </el-space>
        </div>
      </div>
    </el-card>

    <!-- 嵌入代码生成对话框 -->
    <el-dialog
      v-model="showEmbedDialog"
      title="生成嵌入代码"
      width="800px"
    >
      <el-form :model="embedForm" label-width="120px">
        <el-form-item label="嵌入模式">
          <el-select v-model="embedForm.preset" @change="updateEmbedCode">
            <el-option label="iframe模式" value="iframe" />
            <el-option label="最小化模式" value="minimal" />
            <el-option label="简化模式" value="simple" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>

        <template v-if="embedForm.preset === 'custom'">
          <el-form-item label="隐藏头部">
            <el-switch v-model="embedForm.hideHeader" @change="updateEmbedCode" />
          </el-form-item>
          <el-form-item label="隐藏侧边栏">
            <el-switch v-model="embedForm.hideSidebar" @change="updateEmbedCode" />
          </el-form-item>
          <el-form-item label="隐藏标签页">
            <el-switch v-model="embedForm.hideTabs" @change="updateEmbedCode" />
          </el-form-item>
        </template>

        <el-form-item label="目标页面">
          <el-input v-model="embedForm.targetPath" @input="updateEmbedCode" />
        </el-form-item>

        <el-form-item label="iframe宽度">
          <el-input v-model="embedForm.width" @input="updateEmbedCode" />
        </el-form-item>

        <el-form-item label="iframe高度">
          <el-input v-model="embedForm.height" @input="updateEmbedCode" />
        </el-form-item>
      </el-form>

      <div class="embed-code">
        <h4>嵌入代码:</h4>
        <el-input
          v-model="embedCode"
          type="textarea"
          :rows="8"
          readonly
        />
        <div style="margin-top: 10px">
          <el-button type="primary" @click="copyEmbedCode">复制代码</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useLayoutConfig, useLayoutDisplay } from '@/hooks/useLayoutConfig';
import { ElMessage } from 'element-plus';

const { applyPreset, toggleHeader, toggleSidebar, toggleTabs, generateEmbedUrl } = useLayoutConfig();
const layoutDisplay = useLayoutDisplay();

const showEmbedDialog = ref(false);

// 嵌入表单
const embedForm = ref({
  preset: 'iframe',
  hideHeader: false,
  hideSidebar: false,
  hideTabs: false,
  targetPath: '/customer-record/index',
  width: '100%',
  height: '600px'
});

// 生成的嵌入代码
const embedCode = ref('');

// 预设URL
const iframeUrl = computed(() => 
  generateEmbedUrl('/customer-record/index', { preset: 'iframe' })
);

const minimalUrl = computed(() => 
  generateEmbedUrl('/customer-record/index', { preset: 'minimal' })
);

// 更新嵌入代码
const updateEmbedCode = () => {
  let url = '';
  
  if (embedForm.value.preset === 'custom') {
    url = generateEmbedUrl(embedForm.value.targetPath, {
      hideHeader: embedForm.value.hideHeader,
      hideSidebar: embedForm.value.hideSidebar,
      hideTabs: embedForm.value.hideTabs
    });
  } else {
    url = generateEmbedUrl(embedForm.value.targetPath, {
      preset: embedForm.value.preset
    });
  }

  embedCode.value = `<iframe 
  src="${url}" 
  width="${embedForm.value.width}" 
  height="${embedForm.value.height}" 
  frameborder="0"
  style="border: none;">
</iframe>`;
};

// 复制URL
const copyUrl = async (url: string) => {
  try {
    await navigator.clipboard.writeText(url);
    ElMessage.success('URL已复制到剪贴板');
  } catch (err) {
    ElMessage.error('复制失败');
  }
};

// 复制嵌入代码
const copyEmbedCode = async () => {
  try {
    await navigator.clipboard.writeText(embedCode.value);
    ElMessage.success('嵌入代码已复制到剪贴板');
  } catch (err) {
    ElMessage.error('复制失败');
  }
};

// 初始化嵌入代码
updateEmbedCode();
</script>

<style scoped>
.embed-demo {
  padding: 20px;
}

.demo-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.demo-content {
  space-y: 20px;
}

.demo-controls,
.demo-urls {
  margin-top: 30px;
}

.embed-code {
  margin-top: 20px;
}
</style>
